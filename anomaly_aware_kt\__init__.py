"""
异常感知知识追踪框架

基于博弈论的异常检测和知识追踪框架，包含：
1. 博弈论异常检测训练
2. 科学异常生成
3. 异常感知的知识追踪模型
4. 完整的训练和评估流程
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "Anomaly-Aware Knowledge Tracing Team"

# 导入核心模块
try:
    # 博弈论模块
    from . import game_theory
    from .game_theory import (
        GameTheoreticTrainer,
        TeacherAgent,
        StudentAgent,
        AdversaryAgent,
        GameEnvironment,
        TrainingConfig
    )
except ImportError:
    pass

try:
    # 异常生成模块
    from . import anomaly_generation
    from .anomaly_generation import (
        ScientificAnomalyGenerator,
        IRTDifficultyModel,
        QualityController,
        AnomalyTaxonomy
    )
except ImportError:
    pass

try:
    # 核心知识追踪模块
    from . import anomaly_kt
    from .anomaly_kt import (
        AnomalyAwareDTransformer,
        CausalAnomalyDetector,
        AnomalyGenerator,
        AnomalyAwareTrainer
    )
except ImportError:
    pass

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    
    # 博弈论模块
    "game_theory",
    "GameTheoreticTrainer",
    "TeacherAgent",
    "StudentAgent", 
    "AdversaryAgent",
    "GameEnvironment",
    "TrainingConfig",
    
    # 异常生成模块
    "anomaly_generation",
    "ScientificAnomalyGenerator",
    "IRTDifficultyModel",
    "QualityController",
    "AnomalyTaxonomy",
    
    # 核心模块
    "anomaly_kt",
    "AnomalyAwareDTransformer",
    "CausalAnomalyDetector",
    "AnomalyGenerator",
    "AnomalyAwareTrainer",
]
