"""
科学异常生成模块

基于认知科学理论的异常生成框架，包含：
1. 异常分类体系 - 基于认知科学的四大类异常
2. IRT难度建模 - 项目反应理论的难度估算
3. 基础异常生成器 - 统一的异常生成接口

模块结构：
- taxonomy/: 异常分类体系
- irt_modeling/: IRT难度建模
- generators/: 异常生成器实现
- quality/: 质量控制和验证
- utils/: 工具函数
"""

from .taxonomy import (
    AnomalyTaxonomy,
    CognitiveLoadAnomaly
)

from .irt_modeling import (
    IRTDifficultyModel,
    ItemResponseTheoryEstimator,
    DifficultyCalibrator
)

from .generators import (
    ScientificAnomalyGenerator,
    BaseAnomalyStrategy,
    AnomalyGeneratorFactory
)

from .quality import (
    QualityController,
    CognitiveValidator,
    StatisticalValidator
)

__version__ = "1.0.0"
__author__ = "Anomaly-Aware Knowledge Tracing Team"

__all__ = [
    # 异常分类体系
    "AnomalyTaxonomy",
    "CognitiveLoadAnomaly",

    # IRT难度建模
    "IRTDifficultyModel",
    "ItemResponseTheoryEstimator",
    "DifficultyCalibrator",

    # 异常生成器
    "ScientificAnomalyGenerator",
    "BaseAnomalyStrategy",
    "AnomalyGeneratorFactory",

    # 质量控制
    "QualityController",
    "CognitiveValidator",
    "StatisticalValidator",
]
