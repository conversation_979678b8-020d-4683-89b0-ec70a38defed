# 科学异常生成器配置文件
# 基于认知科学理论和IRT难度建模的异常生成配置

# 全局配置
global:
  random_seed: 42
  device: "auto"
  enable_quality_control: true
  min_sequence_length: 5
  max_anomaly_density: 0.5

# 异常生成参数
generation:
  default_anomaly_ratio: 0.15
  quality_threshold: 0.7
  max_retries: 3
  
  # 策略权重
  strategy_weights:
    cognitive_load: 0.30      # 认知负荷异常
    metacognitive: 0.25       # 元认知异常
    motivational: 0.25        # 动机异常
    external_interference: 0.20 # 外部干扰异常

# IRT难度建模配置
irt_modeling:
  model_type: "2PL"           # 1PL, 2PL, 3PL
  auto_calibration: true      # 自动校准
  calibration_method: "em"    # EM算法
  max_iterations: 100
  convergence_tolerance: 1e-4
  
  # 参数约束
  constraints:
    difficulty: [-4.0, 4.0]
    discrimination: [0.1, 3.0]
    guessing: [0.0, 0.3]

# 异常策略配置
strategies:
  cognitive_load:
    name: "认知负荷异常"
    theoretical_basis: "Cognitive Load Theory (<PERSON><PERSON><PERSON>, 1988)"
    
    parameters:
      intrinsic_weight: 0.6
      extraneous_weight: 0.3
      germane_weight: 0.1
      overload_threshold: 0.8
      fatigue_onset: 0.6
      
    intensity_range: [0.2, 0.9]
    duration_range: [5, 20]
    
    manifestations:
      fatigue_effect:
        probability: 0.4
        duration: [10, 30]
        intensity_progression: "exponential"
        
      overload_response:
        probability: 0.3
        duration: [3, 8]
        intensity_progression: "uniform"
        
      attention_lapse:
        probability: 0.3
        duration: [1, 5]
        intensity_progression: "gaussian"

  metacognitive:
    name: "元认知异常"
    theoretical_basis: "Metacognitive Theory (Flavell, 1976)"
    
    parameters:
      overconfidence_factor: 0.3
      underconfidence_factor: 0.2
      strategy_error_rate: 0.15
      prerequisite_threshold: 0.5
      
    intensity_range: [0.1, 0.8]
    duration_range: [3, 12]
    
    manifestations:
      overconfidence:
        probability: 0.4
        description: "跳过前置技能直接尝试高级任务"
        
      underconfidence:
        probability: 0.3
        description: "避免适当难度的挑战"
        
      poor_strategy:
        probability: 0.3
        description: "使用不当的问题解决策略"

  motivational:
    name: "动机异常"
    theoretical_basis: "Self-Determination Theory (Deci & Ryan, 1985)"
    
    parameters:
      disengagement_threshold: 0.3
      gaming_probability: 0.2
      anxiety_factor: 0.25
      
    intensity_range: [0.2, 0.9]
    duration_range: [5, 25]
    
    manifestations:
      disengagement:
        probability: 0.4
        description: "学习动机下降导致的敷衍行为"
        
      gaming_behavior:
        probability: 0.3
        description: "利用系统漏洞而非真正学习"
        
      performance_anxiety:
        probability: 0.3
        description: "焦虑导致的表现下降"

  external_interference:
    name: "外部干扰异常"
    theoretical_basis: "Contextual Learning Theory"
    
    parameters:
      technical_failure_rate: 0.1
      distraction_probability: 0.3
      cheating_detection_threshold: 0.8
      
    intensity_range: [0.3, 1.0]
    duration_range: [1, 10]
    
    manifestations:
      technical_issues:
        probability: 0.3
        description: "系统故障或网络问题"
        
      environmental_distraction:
        probability: 0.4
        description: "环境因素导致的注意力分散"
        
      cheating_behavior:
        probability: 0.3
        description: "外部帮助或资源使用"

# 质量控制配置
quality_control:
  # 质量阈值
  thresholds:
    cognitive_consistency: 0.7
    statistical_quality: 0.7
    overall_quality: 0.7
    
  # 指标权重
  metric_weights:
    cognitive: 0.4
    statistical: 0.4
    other: 0.2
    
  # 验证项目
  validation_checks:
    difficulty_consistency: true
    temporal_consistency: true
    learning_consistency: true
    distribution_quality: true
    change_reasonableness: true
    density_control: true

# 数据集特定配置
dataset_specific:
  assist09:
    strategy_weights:
      cognitive_load: 0.35
      metacognitive: 0.30
      motivational: 0.20
      external_interference: 0.15
    default_anomaly_ratio: 0.12
    
  assist17:
    strategy_weights:
      cognitive_load: 0.30
      metacognitive: 0.25
      motivational: 0.25
      external_interference: 0.20
    default_anomaly_ratio: 0.15
    
  algebra05:
    strategy_weights:
      cognitive_load: 0.25
      metacognitive: 0.35
      motivational: 0.25
      external_interference: 0.15
    default_anomaly_ratio: 0.10
    
  statics:
    strategy_weights:
      cognitive_load: 0.40
      metacognitive: 0.20
      motivational: 0.25
      external_interference: 0.15
    default_anomaly_ratio: 0.18

# 实验配置
experimental:
  # 消融研究
  ablation_study:
    single_strategy_tests: true
    strategy_combination_tests: true
    irt_vs_simple_difficulty: true
    
  # 参数敏感性分析
  sensitivity_analysis:
    anomaly_ratio_range: [0.05, 0.30]
    intensity_range: [0.1, 0.9]
    strategy_weight_variations: 0.1
    
  # 基准测试
  benchmarking:
    baseline_methods: ["random", "pattern_based", "difficulty_agnostic"]
    evaluation_metrics: ["cognitive_validity", "statistical_quality", "downstream_performance"]

# 日志和调试
logging:
  level: "INFO"
  save_generation_logs: true
  log_quality_metrics: true
  log_strategy_performance: true
  
debugging:
  visualize_anomalies: false
  save_intermediate_results: false
  detailed_validation_reports: false

# 性能优化
performance:
  batch_processing: true
  parallel_strategy_execution: false  # 暂时关闭并行处理
  memory_efficient_mode: true
  cache_irt_parameters: true
