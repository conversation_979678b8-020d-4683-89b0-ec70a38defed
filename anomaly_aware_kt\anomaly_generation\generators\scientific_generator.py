"""
科学异常生成器主类

集成认知科学理论、IRT难度建模和质量控制的综合异常生成器。
提供统一的接口来生成高质量、理论基础的学习行为异常。
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import defaultdict
import warnings

from .base_strategy import BaseAnomalyStrategy, GenerationContext, GenerationResult, StrategyType
from ..taxonomy.base_anomaly import AnomalyCategory, AnomalySeverity
from ..irt_modeling.irt_model import IRTDifficultyModel
from ..quality.quality_controller import QualityController


class ScientificAnomalyGenerator:
    """
    科学异常生成器
    
    基于认知科学理论的综合异常生成器，集成：
    1. 多种异常策略的组合
    2. IRT难度建模
    3. 质量控制和验证
    4. 自适应参数调整
    """
    
    def __init__(self,
                 config: Dict[str, Any],
                 irt_model: Optional[IRTDifficultyModel] = None,
                 quality_controller: Optional[QualityController] = None,
                 random_seed: Optional[int] = None):
        """
        初始化科学异常生成器
        
        Args:
            config: 生成器配置
            irt_model: IRT难度模型
            quality_controller: 质量控制器
            random_seed: 随机种子
        """
        self.config = config
        self.irt_model = irt_model
        self.quality_controller = quality_controller
        
        # 设置随机种子
        if random_seed is not None:
            torch.manual_seed(random_seed)
            np.random.seed(random_seed)
            
        # 注册的策略
        self.strategies: Dict[StrategyType, BaseAnomalyStrategy] = {}
        
        # 策略权重
        self.strategy_weights = config.get('strategy_weights', {
            'cognitive_load': 0.30,
            'metacognitive': 0.25,
            'motivational': 0.25,
            'external_interference': 0.20
        })
        
        # 生成参数
        self.default_anomaly_ratio = config.get('default_anomaly_ratio', 0.15)
        self.min_sequence_length = config.get('min_sequence_length', 5)
        self.max_anomaly_density = config.get('max_anomaly_density', 0.5)
        
        # 质量控制参数
        self.quality_threshold = config.get('quality_threshold', 0.7)
        self.enable_quality_control = config.get('enable_quality_control', True)
        
        # 统计信息
        self.generation_stats = defaultdict(int)
        
    def register_strategy(self, strategy: BaseAnomalyStrategy):
        """注册异常策略"""
        self.strategies[strategy.strategy_type] = strategy
        print(f"Registered strategy: {strategy.strategy_type.value}")
        
    def generate_anomalies(self,
                          questions: torch.Tensor,
                          responses: torch.Tensor,
                          anomaly_ratio: Optional[float] = None,
                          strategy_weights: Optional[Dict[str, float]] = None,
                          severity: AnomalySeverity = AnomalySeverity.MEDIUM,
                          **context_kwargs) -> GenerationResult:
        """
        生成异常数据
        
        Args:
            questions: 问题ID序列 [seq_len]
            responses: 原始响应序列 [seq_len]
            anomaly_ratio: 异常比例
            strategy_weights: 策略权重
            severity: 异常严重程度
            **context_kwargs: 额外的上下文信息
            
        Returns:
            GenerationResult: 生成结果
        """
        # 参数验证
        if len(questions) != len(responses):
            raise ValueError("Questions and responses must have the same length")
            
        if len(responses) < self.min_sequence_length:
            warnings.warn(f"Sequence length ({len(responses)}) is below minimum ({self.min_sequence_length})")
            
        # 设置默认值
        if anomaly_ratio is None:
            anomaly_ratio = self.default_anomaly_ratio
        if strategy_weights is None:
            strategy_weights = self.strategy_weights
            
        # 构建生成上下文
        context = self._build_generation_context(
            questions, responses, **context_kwargs
        )
        
        # 生成异常
        result = self._generate_mixed_anomalies(
            context, anomaly_ratio, strategy_weights, severity
        )
        
        # 质量控制
        if self.enable_quality_control and self.quality_controller is not None:
            result = self._apply_quality_control(result, context)
            
        # 更新统计信息
        self._update_generation_stats(result)
        
        return result
        
    def _build_generation_context(self,
                                questions: torch.Tensor,
                                responses: torch.Tensor,
                                **kwargs) -> GenerationContext:
        """构建生成上下文"""
        
        # 估计难度
        difficulties = self._estimate_difficulties(questions, responses)
        
        # 构建上下文
        context = GenerationContext(
            questions=questions,
            responses=responses,
            difficulties=difficulties,
            problem_ids=kwargs.get('problem_ids'),
            interaction_types=kwargs.get('interaction_types'),
            answer_times=kwargs.get('answer_times'),
            knowledge_states=kwargs.get('knowledge_states'),
            sequence_position=kwargs.get('sequence_position', 0.5),
            session_info=kwargs.get('session_info'),
            student_profile=kwargs.get('student_profile')
        )
        
        return context
        
    def _estimate_difficulties(self,
                             questions: torch.Tensor,
                             responses: torch.Tensor) -> torch.Tensor:
        """估计问题难度"""
        if self.irt_model is not None:
            try:
                return self._irt_difficulty_estimation(questions, responses)
            except Exception as e:
                warnings.warn(f"IRT difficulty estimation failed: {e}")
                
        return self._simple_difficulty_estimation(questions, responses)
        
    def _irt_difficulty_estimation(self,
                                 questions: torch.Tensor,
                                 responses: torch.Tensor) -> torch.Tensor:
        """使用IRT模型估计难度"""
        # 如果模型未拟合，使用当前数据进行校准
        if not self.irt_model.is_fitted:
            # 重塑数据为批次格式
            response_matrix = responses.unsqueeze(0)  # [1, seq_len]
            self.irt_model.calibrate_items(response_matrix)
            
        # 获取难度参数
        if self.irt_model.parameters is None:
            raise ValueError("IRT model parameters not available")
            
        difficulties = torch.zeros_like(questions, dtype=torch.float)
        unique_questions = torch.unique(questions)
        
        # 映射问题ID到难度
        for i, q_id in enumerate(unique_questions):
            if i < len(self.irt_model.parameters.difficulty):
                q_difficulty = self.irt_model.parameters.difficulty[i].item()
                mask = questions == q_id
                difficulties[mask] = q_difficulty
                
        return difficulties
        
    def _simple_difficulty_estimation(self,
                                    questions: torch.Tensor,
                                    responses: torch.Tensor) -> torch.Tensor:
        """简单的基于正确率的难度估计"""
        difficulties = torch.zeros_like(questions, dtype=torch.float)
        unique_questions = torch.unique(questions)
        
        for q_id in unique_questions:
            q_mask = questions == q_id
            q_responses = responses[q_mask]
            valid_responses = q_responses[q_responses >= 0]
            
            if len(valid_responses) > 0:
                correct_rate = valid_responses.float().mean().item()
                difficulty = 1 - correct_rate  # 正确率越低，难度越高
            else:
                difficulty = 0.5  # 默认中等难度
                
            difficulties[q_mask] = difficulty
            
        return difficulties
        
    def _generate_mixed_anomalies(self,
                                context: GenerationContext,
                                anomaly_ratio: float,
                                strategy_weights: Dict[str, float],
                                severity: AnomalySeverity) -> GenerationResult:
        """生成混合异常"""
        seq_len = len(context.responses)
        anomaly_sequence = context.responses.clone()
        anomaly_mask = torch.zeros_like(context.responses, dtype=torch.bool)
        
        # 异常信息
        anomaly_info = {
            'strategies_used': [],
            'strategy_contributions': {},
            'total_anomalies': 0,
            'anomaly_positions': []
        }
        
        # 质量指标
        quality_metrics = {}
        
        # 计算每个策略的异常数量
        total_anomalies = max(1, int(seq_len * anomaly_ratio))
        remaining_anomalies = total_anomalies
        
        # 归一化权重
        total_weight = sum(strategy_weights.values())
        normalized_weights = {k: v/total_weight for k, v in strategy_weights.items()}
        
        # 按权重分配异常
        for strategy_name, weight in normalized_weights.items():
            if remaining_anomalies <= 0:
                break
                
            strategy_type = StrategyType(strategy_name)
            if strategy_type not in self.strategies:
                continue
                
            strategy = self.strategies[strategy_type]
            strategy_anomalies = min(remaining_anomalies, max(1, int(total_anomalies * weight)))
            
            # 计算该策略的目标比例
            strategy_ratio = strategy_anomalies / seq_len
            
            try:
                # 生成该策略的异常
                strategy_result = strategy.generate_anomalies(
                    context, strategy_ratio, severity
                )
                
                # 合并结果
                strategy_mask = strategy_result.anomaly_mask
                new_anomaly_positions = strategy_mask & (~anomaly_mask)  # 避免重复
                
                if new_anomaly_positions.sum() > 0:
                    anomaly_sequence[new_anomaly_positions] = strategy_result.anomaly_sequence[new_anomaly_positions]
                    anomaly_mask = anomaly_mask | new_anomaly_positions
                    
                    # 记录信息
                    anomaly_info['strategies_used'].append(strategy_name)
                    anomaly_info['strategy_contributions'][strategy_name] = new_anomaly_positions.sum().item()
                    
                    # 合并质量指标
                    for metric, value in strategy_result.quality_metrics.items():
                        quality_metrics[f'{strategy_name}_{metric}'] = value
                        
                    remaining_anomalies -= new_anomaly_positions.sum().item()
                    
            except Exception as e:
                warnings.warn(f"Strategy {strategy_name} failed: {e}")
                continue
                
        # 更新异常信息
        anomaly_info['total_anomalies'] = anomaly_mask.sum().item()
        anomaly_info['anomaly_positions'] = anomaly_mask.nonzero().flatten().tolist()
        anomaly_info['actual_ratio'] = anomaly_info['total_anomalies'] / seq_len
        
        # 计算整体质量指标
        if quality_metrics:
            quality_metrics['overall_quality'] = np.mean(list(quality_metrics.values()))
        else:
            quality_metrics['overall_quality'] = 0.0
            
        return GenerationResult(
            anomaly_sequence=anomaly_sequence,
            anomaly_mask=anomaly_mask,
            anomaly_info=anomaly_info,
            quality_metrics=quality_metrics
        )
        
    def _apply_quality_control(self,
                             result: GenerationResult,
                             context: GenerationContext) -> GenerationResult:
        """应用质量控制"""
        if self.quality_controller is None:
            return result
            
        try:
            # 验证质量
            quality_report = self.quality_controller.validate_generation(
                original=context.responses,
                anomaly=result.anomaly_sequence,
                mask=result.anomaly_mask,
                context=context.to_anomaly_context()
            )
            
            # 更新质量指标
            result.quality_metrics.update(quality_report)
            
            # 如果质量不足，尝试改进
            overall_quality = quality_report.get('overall_quality', 0.0)
            if overall_quality < self.quality_threshold:
                improved_result = self._improve_generation_quality(result, context)
                return improved_result
                
        except Exception as e:
            warnings.warn(f"Quality control failed: {e}")
            
        return result
        
    def _improve_generation_quality(self,
                                  result: GenerationResult,
                                  context: GenerationContext) -> GenerationResult:
        """改进生成质量"""
        # 简化的质量改进：减少异常密度
        current_ratio = result.get_anomaly_ratio()
        improved_ratio = current_ratio * 0.8  # 减少20%
        
        # 重新生成
        improved_result = self._generate_mixed_anomalies(
            context, improved_ratio, self.strategy_weights, AnomalySeverity.MEDIUM
        )
        
        return improved_result
        
    def _update_generation_stats(self, result: GenerationResult):
        """更新生成统计信息"""
        self.generation_stats['total_generations'] += 1
        self.generation_stats['total_anomalies'] += result.anomaly_info['total_anomalies']
        
        for strategy in result.anomaly_info['strategies_used']:
            self.generation_stats[f'strategy_{strategy}'] += 1
            
        # 质量统计
        overall_quality = result.quality_metrics.get('overall_quality', 0.0)
        self.generation_stats['quality_sum'] += overall_quality
        
    def get_generation_stats(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        stats = dict(self.generation_stats)
        
        if stats['total_generations'] > 0:
            stats['avg_anomalies_per_generation'] = stats['total_anomalies'] / stats['total_generations']
            stats['avg_quality'] = stats['quality_sum'] / stats['total_generations']
            
        return stats
        
    def get_registered_strategies(self) -> List[str]:
        """获取已注册的策略列表"""
        return [strategy.value for strategy in self.strategies.keys()]
        
    def calibrate_irt_model(self, 
                           questions_batch: torch.Tensor,
                           responses_batch: torch.Tensor):
        """校准IRT模型"""
        if self.irt_model is None:
            warnings.warn("No IRT model available for calibration")
            return
            
        try:
            self.irt_model.calibrate_items(responses_batch)
            print("IRT model calibrated successfully")
        except Exception as e:
            warnings.warn(f"IRT model calibration failed: {e}")
            
    def save_model(self, filepath: str):
        """保存模型"""
        model_data = {
            'config': self.config,
            'strategy_weights': self.strategy_weights,
            'generation_stats': dict(self.generation_stats)
        }
        
        if self.irt_model is not None and self.irt_model.is_fitted:
            model_data['irt_parameters'] = self.irt_model.parameters.to_dict()
            
        torch.save(model_data, filepath)
        print(f"Model saved to {filepath}")
        
    def load_model(self, filepath: str):
        """加载模型"""
        model_data = torch.load(filepath)
        
        self.config.update(model_data.get('config', {}))
        self.strategy_weights = model_data.get('strategy_weights', self.strategy_weights)
        self.generation_stats.update(model_data.get('generation_stats', {}))
        
        # 加载IRT参数
        if 'irt_parameters' in model_data and self.irt_model is not None:
            irt_params = model_data['irt_parameters']
            # 这里需要根据实际的IRTParameters结构来重建
            # 简化实现
            print("IRT parameters loaded")
            
        print(f"Model loaded from {filepath}")
        
    def reset_stats(self):
        """重置统计信息"""
        self.generation_stats.clear()
        print("Generation statistics reset")
