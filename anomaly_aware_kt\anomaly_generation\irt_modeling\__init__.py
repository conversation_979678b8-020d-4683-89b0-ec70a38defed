"""
IRT难度建模模块

基于项目反应理论(Item Response Theory)的难度建模框架，包含：
1. IRT参数估计 - 使用EM算法估计项目参数
2. 难度校准 - 多种难度估算方法的集成
3. 自适应难度调整 - 基于学习者表现的动态调整
"""

from .irt_model import IRTDifficultyModel
from .parameter_estimation import ItemResponseTheoryEstimator
from .difficulty_calibrator import DifficultyCalibrator
from .adaptive_difficulty import AdaptiveDifficultyAdjuster

__all__ = [
    "IRTDifficultyModel",
    "ItemResponseTheoryEstimator", 
    "DifficultyCalibrator",
    "AdaptiveDifficultyAdjuster",
]
