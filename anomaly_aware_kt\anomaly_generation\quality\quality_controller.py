"""
综合质量控制器

集成多种验证方法的综合质量控制器，确保生成的异常数据质量。
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from ..taxonomy.base_anomaly import AnomalyContext


class QualityController:
    """
    综合质量控制器
    
    集成认知一致性、统计质量和教育合理性的多维度质量控制。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化质量控制器
        
        Args:
            config: 质量控制配置
        """
        self.config = config
        self.quality_thresholds = config.get('quality_thresholds', {
            'cognitive_consistency': 0.7,
            'statistical_quality': 0.7,
            'overall_quality': 0.7
        })
        
    def validate_generation(self,
                          original: torch.Tensor,
                          anomaly: torch.Tensor,
                          mask: torch.Tensor,
                          context: AnomalyContext) -> Dict[str, float]:
        """
        综合质量验证
        
        Args:
            original: 原始序列
            anomaly: 异常序列
            mask: 异常掩码
            context: 异常上下文
            
        Returns:
            Dict[str, float]: 质量指标
        """
        metrics = {}
        
        # 1. 认知一致性验证
        cognitive_metrics = self._validate_cognitive_consistency(
            original, anomaly, mask, context
        )
        metrics.update({f'cognitive_{k}': v for k, v in cognitive_metrics.items()})
        
        # 2. 统计质量验证
        statistical_metrics = self._validate_statistical_quality(
            original, anomaly, mask
        )
        metrics.update({f'statistical_{k}': v for k, v in statistical_metrics.items()})
        
        # 3. 计算综合质量分数
        metrics['overall_quality'] = self._calculate_overall_quality(metrics)
        
        return metrics
        
    def _validate_cognitive_consistency(self,
                                      original: torch.Tensor,
                                      anomaly: torch.Tensor,
                                      mask: torch.Tensor,
                                      context: AnomalyContext) -> Dict[str, float]:
        """验证认知一致性"""
        metrics = {}
        
        # 1. 难度一致性
        metrics['difficulty_consistency'] = self._check_difficulty_consistency(
            mask, context.difficulty_profile
        )
        
        # 2. 时间一致性
        metrics['temporal_consistency'] = self._check_temporal_consistency(mask)
        
        # 3. 学习进展一致性
        if context.knowledge_state is not None:
            metrics['learning_consistency'] = self._check_learning_consistency(
                original, anomaly, mask, context.knowledge_state
            )
        else:
            metrics['learning_consistency'] = 1.0
            
        return metrics
        
    def _validate_statistical_quality(self,
                                     original: torch.Tensor,
                                     anomaly: torch.Tensor,
                                     mask: torch.Tensor) -> Dict[str, float]:
        """验证统计质量"""
        metrics = {}
        
        # 1. 异常分布质量
        metrics['distribution_quality'] = self._check_anomaly_distribution(mask)
        
        # 2. 变化合理性
        metrics['change_reasonableness'] = self._check_change_reasonableness(
            original, anomaly, mask
        )
        
        # 3. 密度控制
        metrics['density_control'] = self._check_density_control(mask)
        
        return metrics
        
    def _check_difficulty_consistency(self,
                                    mask: torch.Tensor,
                                    difficulties: torch.Tensor) -> float:
        """检查难度一致性"""
        if mask.sum() == 0:
            return 1.0
            
        # 异常应该更多出现在困难题目上
        anomaly_positions = mask.nonzero().flatten()
        anomaly_difficulties = difficulties[anomaly_positions]
        avg_anomaly_difficulty = anomaly_difficulties.mean().item()
        avg_overall_difficulty = difficulties.mean().item()
        
        # 异常题目平均难度应该高于整体平均难度
        consistency = min(1.0, max(0.0, (avg_anomaly_difficulty - avg_overall_difficulty + 0.5)))
        
        return consistency
        
    def _check_temporal_consistency(self, mask: torch.Tensor) -> float:
        """检查时间一致性"""
        if mask.sum() <= 1:
            return 1.0
            
        anomaly_positions = mask.nonzero().flatten()
        
        # 检查异常分布的时间模式
        seq_len = len(mask)
        
        # 计算不同时间窗口的异常密度
        window_size = max(5, seq_len // 4)
        densities = []
        
        for i in range(0, seq_len - window_size + 1, window_size):
            window_mask = mask[i:i+window_size]
            density = window_mask.float().mean().item()
            densities.append(density)
            
        if len(densities) <= 1:
            return 1.0
            
        # 检查是否有合理的时间模式（例如疲劳效应）
        # 计算密度变化的平滑性
        density_changes = np.diff(densities)
        smoothness = 1.0 - np.std(density_changes) / (np.mean(np.abs(density_changes)) + 1e-6)
        
        return max(0.0, min(1.0, smoothness))
        
    def _check_learning_consistency(self,
                                  original: torch.Tensor,
                                  anomaly: torch.Tensor,
                                  mask: torch.Tensor,
                                  knowledge_state: torch.Tensor) -> float:
        """检查学习进展一致性"""
        if mask.sum() == 0:
            return 1.0
            
        anomaly_positions = mask.nonzero().flatten()
        
        # 检查异常位置的知识状态
        knowledge_at_anomalies = knowledge_state[anomaly_positions]
        avg_knowledge_at_anomalies = knowledge_at_anomalies.mean().item()
        
        # 检查异常变化的合理性
        reasonable_changes = 0
        total_changes = 0
        
        for pos in anomaly_positions:
            original_val = original[pos].item()
            anomaly_val = anomaly[pos].item()
            knowledge_level = knowledge_state[pos].item()
            
            total_changes += 1
            
            # 低知识水平时，错误变正确不太合理
            if knowledge_level < 0.3 and original_val == 0 and anomaly_val == 1:
                reasonable_changes += 0.2
            # 高知识水平时，正确变错误可能是失误
            elif knowledge_level > 0.7 and original_val == 1 and anomaly_val == 0:
                reasonable_changes += 0.8
            # 中等知识水平时，变化都比较合理
            elif 0.3 <= knowledge_level <= 0.7:
                reasonable_changes += 0.6
            else:
                reasonable_changes += 0.4
                
        if total_changes == 0:
            return 1.0
            
        return reasonable_changes / total_changes
        
    def _check_anomaly_distribution(self, mask: torch.Tensor) -> float:
        """检查异常分布质量"""
        if mask.sum() == 0:
            return 1.0
            
        anomaly_positions = mask.nonzero().flatten()
        
        if len(anomaly_positions) <= 1:
            return 1.0
            
        # 检查异常间隔的分布
        intervals = torch.diff(anomaly_positions.float())
        
        # 理想情况下，间隔应该有一定的变化但不要太极端
        mean_interval = intervals.mean().item()
        std_interval = intervals.std().item()
        
        if mean_interval == 0:
            return 0.0
            
        # 变异系数应该在合理范围内
        cv = std_interval / mean_interval
        
        # 变异系数在0.5-1.5之间比较合理
        if 0.5 <= cv <= 1.5:
            quality = 1.0
        elif cv < 0.5:
            quality = cv / 0.5  # 太规律
        else:
            quality = max(0.0, 2.0 - cv)  # 太随机
            
        return quality
        
    def _check_change_reasonableness(self,
                                   original: torch.Tensor,
                                   anomaly: torch.Tensor,
                                   mask: torch.Tensor) -> float:
        """检查变化合理性"""
        if mask.sum() == 0:
            return 1.0
            
        anomaly_positions = mask.nonzero().flatten()
        reasonable_changes = 0
        
        for pos in anomaly_positions:
            original_val = original[pos].item()
            anomaly_val = anomaly[pos].item()
            
            # 检查变化是否合理
            if original_val != anomaly_val:  # 确实发生了变化
                reasonable_changes += 1
            else:  # 没有变化，可能是标记错误
                reasonable_changes += 0.1
                
        if len(anomaly_positions) == 0:
            return 1.0
            
        return reasonable_changes / len(anomaly_positions)
        
    def _check_density_control(self, mask: torch.Tensor) -> float:
        """检查密度控制"""
        seq_len = len(mask)
        anomaly_count = mask.sum().item()
        anomaly_ratio = anomaly_count / seq_len
        
        # 异常比例应该在合理范围内
        if 0.05 <= anomaly_ratio <= 0.3:
            return 1.0
        elif anomaly_ratio < 0.05:
            return anomaly_ratio / 0.05  # 太少
        else:
            return max(0.0, (0.5 - anomaly_ratio) / 0.2)  # 太多
            
    def _calculate_overall_quality(self, metrics: Dict[str, float]) -> float:
        """计算综合质量分数"""
        # 权重配置
        weights = self.config.get('metric_weights', {
            'cognitive': 0.4,
            'statistical': 0.4,
            'other': 0.2
        })
        
        # 分类指标
        cognitive_metrics = [v for k, v in metrics.items() if k.startswith('cognitive_')]
        statistical_metrics = [v for k, v in metrics.items() if k.startswith('statistical_')]
        other_metrics = [v for k, v in metrics.items() 
                        if not k.startswith('cognitive_') and not k.startswith('statistical_')]
        
        # 计算加权平均
        cognitive_score = np.mean(cognitive_metrics) if cognitive_metrics else 1.0
        statistical_score = np.mean(statistical_metrics) if statistical_metrics else 1.0
        other_score = np.mean(other_metrics) if other_metrics else 1.0
        
        overall_quality = (weights['cognitive'] * cognitive_score +
                          weights['statistical'] * statistical_score +
                          weights['other'] * other_score)
        
        return overall_quality
