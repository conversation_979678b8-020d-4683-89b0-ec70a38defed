"""
基础异常类定义

定义所有异常类型的基础接口和异常分类体系的核心框架。
基于认知科学理论建立的科学分类体系。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import torch
import numpy as np
from dataclasses import dataclass


class AnomalyCategory(Enum):
    """异常类别枚举"""
    COGNITIVE_LOAD = "cognitive_load"
    METACOGNITIVE = "metacognitive"
    MOTIVATIONAL = "motivational"
    EXTERNAL_INTERFERENCE = "external_interference"


class AnomalySeverity(Enum):
    """异常严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class AnomalyManifestationPattern:
    """异常表现模式"""
    name: str
    description: str
    typical_duration: Tuple[int, int]  # (min_steps, max_steps)
    probability_profile: str  # "uniform", "exponential", "gaussian"
    cognitive_basis: str
    
    
@dataclass
class AnomalyContext:
    """异常生成上下文"""
    sequence_length: int
    difficulty_profile: torch.Tensor
    knowledge_state: torch.Tensor
    temporal_position: float  # 0.0-1.0, 在序列中的相对位置
    session_info: Optional[Dict] = None
    student_profile: Optional[Dict] = None


class BaseAnomaly(ABC):
    """
    基础异常类
    
    所有异常类型的抽象基类，定义了异常生成的统一接口。
    基于认知科学理论，确保每种异常都有明确的理论基础。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化异常类
        
        Args:
            config: 异常配置参数
        """
        self.config = config
        self.category = self._get_category()
        self.theoretical_foundation = self._get_theoretical_foundation()
        self.manifestation_patterns = self._define_manifestation_patterns()
        
    @abstractmethod
    def _get_category(self) -> AnomalyCategory:
        """获取异常类别"""
        pass
        
    @abstractmethod
    def _get_theoretical_foundation(self) -> Dict[str, str]:
        """获取理论基础"""
        pass
        
    @abstractmethod
    def _define_manifestation_patterns(self) -> List[AnomalyManifestationPattern]:
        """定义异常表现模式"""
        pass
        
    @abstractmethod
    def generate_anomaly(self, 
                        sequence: torch.Tensor,
                        context: AnomalyContext,
                        severity: AnomalySeverity = AnomalySeverity.MEDIUM) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        生成异常
        
        Args:
            sequence: 原始序列 [seq_len]
            context: 异常生成上下文
            severity: 异常严重程度
            
        Returns:
            anomaly_sequence: 包含异常的序列
            anomaly_mask: 异常位置掩码
        """
        pass
        
    @abstractmethod
    def validate_anomaly(self, 
                        original: torch.Tensor,
                        anomaly: torch.Tensor,
                        mask: torch.Tensor,
                        context: AnomalyContext) -> Dict[str, float]:
        """
        验证异常质量
        
        Args:
            original: 原始序列
            anomaly: 异常序列
            mask: 异常掩码
            context: 生成上下文
            
        Returns:
            validation_metrics: 验证指标
        """
        pass
        
    def get_anomaly_info(self) -> Dict[str, Any]:
        """获取异常信息"""
        return {
            'category': self.category.value,
            'theoretical_foundation': self.theoretical_foundation,
            'manifestation_patterns': [
                {
                    'name': pattern.name,
                    'description': pattern.description,
                    'duration_range': pattern.typical_duration,
                    'cognitive_basis': pattern.cognitive_basis
                }
                for pattern in self.manifestation_patterns
            ],
            'config': self.config
        }
        
    def _calculate_anomaly_intensity(self, 
                                   severity: AnomalySeverity,
                                   context: AnomalyContext) -> float:
        """计算异常强度"""
        base_intensity = {
            AnomalySeverity.LOW: 0.3,
            AnomalySeverity.MEDIUM: 0.6,
            AnomalySeverity.HIGH: 0.8,
            AnomalySeverity.EXTREME: 0.95
        }[severity]
        
        # 根据上下文调整强度
        context_factor = 1.0
        
        # 序列位置影响
        if context.temporal_position > 0.7:  # 序列后期
            context_factor *= 1.2  # 疲劳效应增强
            
        # 难度影响
        avg_difficulty = context.difficulty_profile.mean().item()
        if avg_difficulty > 0.7:  # 高难度
            context_factor *= 1.1
            
        return min(0.99, base_intensity * context_factor)
        
    def _select_manifestation_pattern(self, 
                                    context: AnomalyContext,
                                    severity: AnomalySeverity) -> AnomalyManifestationPattern:
        """选择异常表现模式"""
        # 简化实现：根据序列长度和严重程度选择
        suitable_patterns = [
            pattern for pattern in self.manifestation_patterns
            if pattern.typical_duration[0] <= context.sequence_length
        ]
        
        if not suitable_patterns:
            suitable_patterns = self.manifestation_patterns
            
        # 根据严重程度选择模式
        if severity in [AnomalySeverity.HIGH, AnomalySeverity.EXTREME]:
            # 选择持续时间较长的模式
            return max(suitable_patterns, key=lambda p: p.typical_duration[1])
        else:
            # 选择持续时间较短的模式
            return min(suitable_patterns, key=lambda p: p.typical_duration[1])


class AnomalyTaxonomy:
    """
    异常分类体系
    
    管理所有异常类型的分类体系，提供统一的异常生成接口。
    """
    
    def __init__(self):
        """初始化异常分类体系"""
        self.anomaly_classes = {}
        self.category_weights = {
            AnomalyCategory.COGNITIVE_LOAD: 0.30,
            AnomalyCategory.METACOGNITIVE: 0.25,
            AnomalyCategory.MOTIVATIONAL: 0.25,
            AnomalyCategory.EXTERNAL_INTERFERENCE: 0.20
        }
        
    def register_anomaly_class(self, anomaly_class: BaseAnomaly):
        """注册异常类"""
        category = anomaly_class.category
        if category not in self.anomaly_classes:
            self.anomaly_classes[category] = []
        self.anomaly_classes[category].append(anomaly_class)
        
    def get_anomaly_by_category(self, category: AnomalyCategory) -> List[BaseAnomaly]:
        """根据类别获取异常类"""
        return self.anomaly_classes.get(category, [])
        
    def generate_mixed_anomalies(self,
                                sequence: torch.Tensor,
                                context: AnomalyContext,
                                anomaly_ratio: float = 0.15,
                                category_weights: Optional[Dict[AnomalyCategory, float]] = None) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        生成混合异常
        
        Args:
            sequence: 原始序列
            context: 生成上下文
            anomaly_ratio: 异常比例
            category_weights: 类别权重
            
        Returns:
            anomaly_sequence: 异常序列
            anomaly_mask: 异常掩码
            anomaly_info: 异常信息
        """
        if category_weights is None:
            category_weights = self.category_weights
            
        seq_len = len(sequence)
        anomaly_sequence = sequence.clone()
        anomaly_mask = torch.zeros_like(sequence, dtype=torch.bool)
        anomaly_info = {'categories': [], 'patterns': [], 'positions': []}
        
        # 计算每个类别的异常数量
        total_anomalies = int(seq_len * anomaly_ratio)
        
        for category, weight in category_weights.items():
            if category not in self.anomaly_classes:
                continue
                
            category_anomalies = int(total_anomalies * weight)
            if category_anomalies == 0:
                continue
                
            # 随机选择该类别的一个异常类
            anomaly_classes = self.anomaly_classes[category]
            selected_anomaly = np.random.choice(anomaly_classes)
            
            # 生成异常
            try:
                anomaly_seq, anomaly_positions = selected_anomaly.generate_anomaly(
                    sequence, context, AnomalySeverity.MEDIUM
                )
                
                # 合并异常
                anomaly_sequence = anomaly_seq
                anomaly_mask = anomaly_mask | anomaly_positions
                
                # 记录异常信息
                anomaly_info['categories'].append(category.value)
                anomaly_info['patterns'].append(selected_anomaly.manifestation_patterns[0].name)
                anomaly_info['positions'].append(anomaly_positions.nonzero().flatten().tolist())
                
            except Exception as e:
                print(f"Warning: Failed to generate {category.value} anomaly: {e}")
                continue
                
        return anomaly_sequence, anomaly_mask, anomaly_info
        
    def get_taxonomy_info(self) -> Dict[str, Any]:
        """获取分类体系信息"""
        info = {
            'total_categories': len(self.anomaly_classes),
            'category_weights': {cat.value: weight for cat, weight in self.category_weights.items()},
            'categories': {}
        }
        
        for category, anomaly_classes in self.anomaly_classes.items():
            info['categories'][category.value] = {
                'count': len(anomaly_classes),
                'classes': [cls.__class__.__name__ for cls in anomaly_classes]
            }
            
        return info
        
    def validate_taxonomy_completeness(self) -> Dict[str, bool]:
        """验证分类体系完整性"""
        required_categories = set(AnomalyCategory)
        available_categories = set(self.anomaly_classes.keys())
        
        return {
            'complete': required_categories.issubset(available_categories),
            'missing_categories': [cat.value for cat in required_categories - available_categories],
            'coverage': len(available_categories) / len(required_categories)
        }
