"""
认知负荷异常实现

基于Sweller的认知负荷理论(Cognitive Load Theory)实现的异常类型。
认知负荷理论将认知负荷分为三类：
1. 内在负荷 (Intrinsic Load) - 任务本身的复杂性
2. 外在负荷 (Extraneous Load) - 不良教学设计造成的负荷
3. 相关负荷 (Germane Load) - 用于构建图式的负荷

当总认知负荷超过工作记忆容量时，会产生各种异常行为。
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from .base_anomaly import (
    BaseAnomaly, AnomalyCategory, AnomalySeverity, 
    AnomalyManifestationPattern, AnomalyContext
)


class CognitiveLoadAnomaly(BaseAnomaly):
    """
    认知负荷异常类
    
    基于认知负荷理论的异常生成，模拟学习者在认知负荷过载时的异常行为：
    - 疲劳效应：长时间学习导致的性能下降
    - 过载响应：复杂任务导致的随机行为
    - 注意力衰减：注意力资源耗尽导致的错误
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化认知负荷异常
        
        Args:
            config: 配置参数
                - intrinsic_weight: 内在负荷权重 (默认: 0.6)
                - extraneous_weight: 外在负荷权重 (默认: 0.3)
                - germane_weight: 相关负荷权重 (默认: 0.1)
                - overload_threshold: 过载阈值 (默认: 0.8)
                - fatigue_onset: 疲劳开始位置 (默认: 0.6)
        """
        super().__init__(config)
        
        # 认知负荷参数
        self.intrinsic_weight = config.get('intrinsic_weight', 0.6)
        self.extraneous_weight = config.get('extraneous_weight', 0.3)
        self.germane_weight = config.get('germane_weight', 0.1)
        self.overload_threshold = config.get('overload_threshold', 0.8)
        self.fatigue_onset = config.get('fatigue_onset', 0.6)
        
    def _get_category(self) -> AnomalyCategory:
        """获取异常类别"""
        return AnomalyCategory.COGNITIVE_LOAD
        
    def _get_theoretical_foundation(self) -> Dict[str, str]:
        """获取理论基础"""
        return {
            'theory': 'Cognitive Load Theory',
            'author': 'John Sweller (1988)',
            'key_principle': 'Working memory limitations affect learning performance',
            'components': 'Intrinsic + Extraneous + Germane Load',
            'references': [
                'Sweller, J. (1988). Cognitive load during problem solving',
                'Paas, F., et al. (2003). Cognitive load measurement'
            ]
        }
        
    def _define_manifestation_patterns(self) -> List[AnomalyManifestationPattern]:
        """定义异常表现模式"""
        return [
            AnomalyManifestationPattern(
                name="fatigue_effect",
                description="疲劳效应 - 长时间学习导致的性能递减",
                typical_duration=(10, 30),
                probability_profile="exponential",
                cognitive_basis="工作记忆资源耗尽"
            ),
            AnomalyManifestationPattern(
                name="overload_response", 
                description="过载响应 - 复杂任务导致的随机行为",
                typical_duration=(3, 8),
                probability_profile="uniform",
                cognitive_basis="认知负荷超过容量限制"
            ),
            AnomalyManifestationPattern(
                name="attention_lapse",
                description="注意力衰减 - 注意力资源不足导致的错误",
                typical_duration=(1, 5),
                probability_profile="gaussian",
                cognitive_basis="注意力资源分配失衡"
            )
        ]
        
    def generate_anomaly(self, 
                        sequence: torch.Tensor,
                        context: AnomalyContext,
                        severity: AnomalySeverity = AnomalySeverity.MEDIUM) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        生成认知负荷异常
        
        Args:
            sequence: 原始序列 [seq_len]
            context: 异常生成上下文
            severity: 异常严重程度
            
        Returns:
            anomaly_sequence: 包含异常的序列
            anomaly_mask: 异常位置掩码
        """
        seq_len = len(sequence)
        anomaly_sequence = sequence.clone()
        anomaly_mask = torch.zeros_like(sequence, dtype=torch.bool)
        
        # 计算认知负荷
        cognitive_load = self._calculate_cognitive_load(context)
        
        # 选择表现模式
        pattern = self._select_manifestation_pattern(context, severity)
        
        # 根据模式生成异常
        if pattern.name == "fatigue_effect":
            anomaly_sequence, anomaly_mask = self._generate_fatigue_effect(
                sequence, cognitive_load, severity
            )
        elif pattern.name == "overload_response":
            anomaly_sequence, anomaly_mask = self._generate_overload_response(
                sequence, cognitive_load, severity
            )
        elif pattern.name == "attention_lapse":
            anomaly_sequence, anomaly_mask = self._generate_attention_lapse(
                sequence, cognitive_load, severity
            )
            
        return anomaly_sequence, anomaly_mask
        
    def _calculate_cognitive_load(self, context: AnomalyContext) -> torch.Tensor:
        """
        计算认知负荷
        
        基于认知负荷理论的三组件模型：
        Total Load = Intrinsic + Extraneous + Germane
        """
        seq_len = context.sequence_length
        
        # 1. 内在负荷 - 基于任务难度
        intrinsic_load = context.difficulty_profile
        
        # 2. 外在负荷 - 基于序列位置和复杂性
        extraneous_load = torch.zeros(seq_len)
        for i in range(seq_len):
            # 序列位置效应
            position_factor = i / seq_len
            # 任务切换成本
            if i > 0:
                difficulty_change = abs(context.difficulty_profile[i] - context.difficulty_profile[i-1])
                switch_cost = difficulty_change * 0.5
            else:
                switch_cost = 0
            extraneous_load[i] = position_factor * 0.3 + switch_cost
            
        # 3. 相关负荷 - 基于学习进展
        germane_load = torch.zeros(seq_len)
        if context.knowledge_state is not None:
            for i in range(seq_len):
                # 学习努力程度
                if i > 0:
                    learning_effort = abs(context.knowledge_state[i] - context.knowledge_state[i-1])
                    germane_load[i] = learning_effort * 2.0
                    
        # 总认知负荷
        total_load = (self.intrinsic_weight * intrinsic_load + 
                     self.extraneous_weight * extraneous_load +
                     self.germane_weight * germane_load)
                     
        return total_load
        
    def _generate_fatigue_effect(self, 
                               sequence: torch.Tensor,
                               cognitive_load: torch.Tensor,
                               severity: AnomalySeverity) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成疲劳效应异常"""
        seq_len = len(sequence)
        anomaly_sequence = sequence.clone()
        anomaly_mask = torch.zeros_like(sequence, dtype=torch.bool)
        
        # 疲劳开始位置
        fatigue_start = int(seq_len * self.fatigue_onset)
        
        # 疲劳强度随时间递增
        intensity = self._calculate_anomaly_intensity(severity, None)
        
        for i in range(fatigue_start, seq_len):
            # 疲劳程度随时间增加
            fatigue_level = (i - fatigue_start) / (seq_len - fatigue_start)
            current_intensity = intensity * fatigue_level
            
            # 认知负荷影响
            load_factor = min(1.0, cognitive_load[i].item())
            error_probability = current_intensity * load_factor
            
            if torch.rand(1).item() < error_probability:
                # 疲劳导致的错误：正确答案变错误的概率更高
                if sequence[i] == 1:  # 原本正确
                    anomaly_sequence[i] = 0
                    anomaly_mask[i] = True
                elif torch.rand(1).item() < 0.3:  # 30%概率错误变正确
                    anomaly_sequence[i] = 1
                    anomaly_mask[i] = True
                    
        return anomaly_sequence, anomaly_mask
        
    def _generate_overload_response(self,
                                  sequence: torch.Tensor,
                                  cognitive_load: torch.Tensor,
                                  severity: AnomalySeverity) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成过载响应异常"""
        seq_len = len(sequence)
        anomaly_sequence = sequence.clone()
        anomaly_mask = torch.zeros_like(sequence, dtype=torch.bool)
        
        # 找到高负荷区域
        overload_positions = (cognitive_load > self.overload_threshold).nonzero().flatten()
        
        if len(overload_positions) == 0:
            return anomaly_sequence, anomaly_mask
            
        # 选择过载区域
        intensity = self._calculate_anomaly_intensity(severity, None)
        
        for pos in overload_positions:
            if torch.rand(1).item() < intensity:
                # 过载导致随机响应
                anomaly_sequence[pos] = torch.randint(0, 2, (1,)).item()
                anomaly_mask[pos] = True
                
        return anomaly_sequence, anomaly_mask
        
    def _generate_attention_lapse(self,
                                sequence: torch.Tensor,
                                cognitive_load: torch.Tensor,
                                severity: AnomalySeverity) -> Tuple[torch.Tensor, torch.Tensor]:
        """生成注意力衰减异常"""
        seq_len = len(sequence)
        anomaly_sequence = sequence.clone()
        anomaly_mask = torch.zeros_like(sequence, dtype=torch.bool)
        
        # 注意力衰减通常是短暂的、随机的
        intensity = self._calculate_anomaly_intensity(severity, None)
        num_lapses = max(1, int(seq_len * intensity * 0.1))  # 10%的位置可能有注意力衰减
        
        # 随机选择注意力衰减位置
        lapse_positions = torch.randperm(seq_len)[:num_lapses]
        
        for pos in lapse_positions:
            # 注意力衰减导致的错误
            if torch.rand(1).item() < 0.7:  # 70%概率产生错误
                anomaly_sequence[pos] = 1 - anomaly_sequence[pos]
                anomaly_mask[pos] = True
                
        return anomaly_sequence, anomaly_mask
        
    def validate_anomaly(self, 
                        original: torch.Tensor,
                        anomaly: torch.Tensor,
                        mask: torch.Tensor,
                        context: AnomalyContext) -> Dict[str, float]:
        """验证认知负荷异常质量"""
        metrics = {}
        
        # 1. 疲劳进展验证
        metrics['fatigue_progression'] = self._validate_fatigue_progression(mask)
        
        # 2. 负荷相关性验证
        cognitive_load = self._calculate_cognitive_load(context)
        metrics['load_correlation'] = self._validate_load_correlation(mask, cognitive_load)
        
        # 3. 时间一致性验证
        metrics['temporal_consistency'] = self._validate_temporal_consistency(mask)
        
        # 4. 认知合理性验证
        metrics['cognitive_plausibility'] = self._validate_cognitive_plausibility(
            original, anomaly, mask
        )
        
        return metrics
        
    def _validate_fatigue_progression(self, mask: torch.Tensor) -> float:
        """验证疲劳进展的合理性"""
        seq_len = len(mask)
        if seq_len < 10:
            return 1.0
            
        # 计算不同时间窗口的异常密度
        window_size = seq_len // 4
        densities = []
        
        for i in range(0, seq_len - window_size, window_size):
            window_mask = mask[i:i+window_size]
            density = window_mask.float().mean().item()
            densities.append(density)
            
        # 检查是否有递增趋势（疲劳效应）
        if len(densities) > 1:
            correlation = np.corrcoef(range(len(densities)), densities)[0, 1]
            return max(0, correlation)  # 正相关表示合理的疲劳进展
            
        return 1.0
        
    def _validate_load_correlation(self, mask: torch.Tensor, 
                                 cognitive_load: torch.Tensor) -> float:
        """验证异常与认知负荷的相关性"""
        if len(mask) != len(cognitive_load):
            return 0.0
            
        # 计算异常位置与认知负荷的相关性
        anomaly_scores = mask.float()
        load_scores = cognitive_load
        
        if anomaly_scores.sum() == 0:
            return 1.0  # 没有异常时认为是合理的
            
        correlation = torch.corrcoef(torch.stack([anomaly_scores, load_scores]))[0, 1]
        
        # 正相关表示异常与高认知负荷相关，这是合理的
        return max(0, correlation.item()) if not torch.isnan(correlation) else 0.0
        
    def _validate_temporal_consistency(self, mask: torch.Tensor) -> float:
        """验证时间一致性"""
        if mask.sum() == 0:
            return 1.0
            
        # 检查异常分布的时间模式
        anomaly_positions = mask.nonzero().flatten()
        
        if len(anomaly_positions) < 2:
            return 1.0
            
        # 计算异常间隔的变异系数
        intervals = torch.diff(anomaly_positions.float())
        if len(intervals) == 0:
            return 1.0
            
        mean_interval = intervals.mean()
        std_interval = intervals.std()
        
        if mean_interval == 0:
            return 0.0
            
        cv = std_interval / mean_interval  # 变异系数
        
        # 变异系数适中表示时间一致性好
        return max(0, 1 - cv.item() / 2)  # 归一化到[0,1]
        
    def _validate_cognitive_plausibility(self, 
                                       original: torch.Tensor,
                                       anomaly: torch.Tensor,
                                       mask: torch.Tensor) -> float:
        """验证认知合理性"""
        if mask.sum() == 0:
            return 1.0
            
        # 检查异常变化的合理性
        anomaly_positions = mask.nonzero().flatten()
        plausible_changes = 0
        
        for pos in anomaly_positions:
            original_val = original[pos].item()
            anomaly_val = anomaly[pos].item()
            
            # 认知负荷异常通常导致：
            # 1. 正确答案变错误（疲劳、过载）
            # 2. 错误答案偶尔变正确（随机猜测）
            if original_val == 1 and anomaly_val == 0:  # 正确变错误
                plausible_changes += 1
            elif original_val == 0 and anomaly_val == 1:  # 错误变正确
                plausible_changes += 0.3  # 权重较低，因为不太常见
                
        if len(anomaly_positions) == 0:
            return 1.0
            
        plausibility = plausible_changes / len(anomaly_positions)
        return min(1.0, plausibility)
