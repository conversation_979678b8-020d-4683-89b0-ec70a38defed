"""
数据集感知课程策略

教师根据不同数据集（战场）特性制定相应的课程策略。
每种数据集都有其独特的"地形"，需要不同的教学战术。
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import json

from .game_agents import GameState


class DatasetType(Enum):
    """数据集类型"""
    KNOWLEDGE_TRACING = "knowledge_tracing"
    CYBERSECURITY = "cybersecurity"
    FINANCIAL = "financial"
    MEDICAL = "medical"
    GENERAL = "general"


class BattlefieldCharacteristics(Enum):
    """战场特征"""
    HIGH_NOISE = "high_noise"              # 高噪声环境
    IMBALANCED = "imbalanced"              # 不平衡数据
    HIGH_DIMENSIONAL = "high_dimensional"   # 高维特征
    TEMPORAL = "temporal"                  # 时序特性
    SPARSE = "sparse"                      # 稀疏特征
    MULTI_MODAL = "multi_modal"            # 多模态
    ADVERSARIAL_PRONE = "adversarial_prone" # 易受对抗攻击


@dataclass
class DatasetProfile:
    """数据集画像"""
    dataset_type: DatasetType
    characteristics: List[BattlefieldCharacteristics]
    
    # 统计特性
    sample_count: int
    feature_dimension: int
    anomaly_ratio: float
    difficulty_distribution: Dict[str, float]
    
    # 领域特性
    domain_complexity: float        # 领域复杂度 [0,1]
    noise_level: float             # 噪声水平 [0,1]
    adversarial_vulnerability: float # 对抗脆弱性 [0,1]
    
    # 学习特性
    learning_curve_steepness: float # 学习曲线陡峭度
    plateau_tendency: float        # 平台期倾向
    forgetting_rate: float         # 遗忘率


class DatasetAwareCurriculumDesigner:
    """
    数据集感知课程设计器
    
    根据不同数据集的特性，为教师提供定制化的课程策略。
    每种数据集都被视为不同的"战场"，需要相应的"战术"。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化课程设计器
        
        Args:
            config: 配置参数
        """
        self.config = config
        
        # 预定义的数据集策略模板
        self.strategy_templates = self._load_strategy_templates()
        
        # 动态学习的策略调整
        self.learned_adjustments = {}
        
        # 性能历史
        self.performance_history = {}
        
    def analyze_dataset_profile(self, dataset_samples: List[Any]) -> DatasetProfile:
        """
        分析数据集画像
        
        Args:
            dataset_samples: 数据集样本
            
        Returns:
            DatasetProfile: 数据集画像
        """
        # 基础统计分析
        sample_count = len(dataset_samples)
        feature_dimension = len(dataset_samples[0].features) if dataset_samples else 0
        anomaly_ratio = sum(1 for s in dataset_samples if s.label == 1) / sample_count if sample_count > 0 else 0
        
        # 难度分布分析
        difficulties = [s.difficulty for s in dataset_samples]
        difficulty_distribution = {
            'mean': np.mean(difficulties),
            'std': np.std(difficulties),
            'skewness': self._calculate_skewness(difficulties),
            'kurtosis': self._calculate_kurtosis(difficulties)
        }
        
        # 识别数据集类型
        dataset_type = self._identify_dataset_type(dataset_samples)
        
        # 识别战场特征
        characteristics = self._identify_battlefield_characteristics(dataset_samples)
        
        # 计算领域特性
        domain_complexity = self._calculate_domain_complexity(dataset_samples)
        noise_level = self._estimate_noise_level(dataset_samples)
        adversarial_vulnerability = self._assess_adversarial_vulnerability(dataset_samples)
        
        # 计算学习特性
        learning_curve_steepness = self._estimate_learning_steepness(dataset_type, characteristics)
        plateau_tendency = self._estimate_plateau_tendency(dataset_type, difficulty_distribution)
        forgetting_rate = self._estimate_forgetting_rate(dataset_type, domain_complexity)
        
        return DatasetProfile(
            dataset_type=dataset_type,
            characteristics=characteristics,
            sample_count=sample_count,
            feature_dimension=feature_dimension,
            anomaly_ratio=anomaly_ratio,
            difficulty_distribution=difficulty_distribution,
            domain_complexity=domain_complexity,
            noise_level=noise_level,
            adversarial_vulnerability=adversarial_vulnerability,
            learning_curve_steepness=learning_curve_steepness,
            plateau_tendency=plateau_tendency,
            forgetting_rate=forgetting_rate
        )
        
    def design_curriculum_strategy(self, 
                                 dataset_profile: DatasetProfile,
                                 current_game_state: GameState,
                                 performance_history: List[Dict]) -> Dict[str, Any]:
        """
        设计数据集感知的课程策略
        
        Args:
            dataset_profile: 数据集画像
            current_game_state: 当前博弈状态
            performance_history: 性能历史
            
        Returns:
            Dict[str, Any]: 课程策略
        """
        
        # 1. 获取基础策略模板
        base_strategy = self._get_base_strategy_template(dataset_profile.dataset_type)
        
        # 2. 根据战场特征调整策略
        battlefield_adjusted_strategy = self._adjust_for_battlefield_characteristics(
            base_strategy, dataset_profile.characteristics
        )
        
        # 3. 根据数据集统计特性微调
        statistically_tuned_strategy = self._tune_for_statistical_properties(
            battlefield_adjusted_strategy, dataset_profile
        )
        
        # 4. 根据当前博弈状态动态调整
        dynamically_adjusted_strategy = self._adjust_for_game_state(
            statistically_tuned_strategy, current_game_state, dataset_profile
        )
        
        # 5. 基于历史性能优化
        optimized_strategy = self._optimize_based_on_history(
            dynamically_adjusted_strategy, performance_history, dataset_profile
        )
        
        # 6. 添加创新性探索
        final_strategy = self._add_exploration_elements(
            optimized_strategy, dataset_profile, current_game_state
        )
        
        return final_strategy
        
    def _get_base_strategy_template(self, dataset_type: DatasetType) -> Dict[str, Any]:
        """获取基础策略模板"""
        
        if dataset_type == DatasetType.KNOWLEDGE_TRACING:
            return {
                'curriculum_philosophy': 'progressive_mastery',
                'stage_transitions': {
                    'foundation_to_adversarial': 0.75,  # 更高的要求
                    'adversarial_to_mastery': 0.85
                },
                'difficulty_progression': 'adaptive_spiral',
                'intervention_style': 'scaffolding',
                'feedback_frequency': 'high',
                'reward_structure': {
                    'learning_progress_weight': 0.4,
                    'retention_weight': 0.3,
                    'transfer_weight': 0.3
                },
                'special_considerations': {
                    'prerequisite_checking': True,
                    'skill_transfer_modeling': True,
                    'forgetting_curve_awareness': True
                }
            }
            
        elif dataset_type == DatasetType.CYBERSECURITY:
            return {
                'curriculum_philosophy': 'threat_driven',
                'stage_transitions': {
                    'foundation_to_adversarial': 0.8,   # 更严格的标准
                    'adversarial_to_mastery': 0.9
                },
                'difficulty_progression': 'threat_escalation',
                'intervention_style': 'alert_guidance',
                'feedback_frequency': 'real_time',
                'reward_structure': {
                    'detection_accuracy_weight': 0.5,
                    'false_positive_penalty': 0.3,
                    'response_time_weight': 0.2
                },
                'special_considerations': {
                    'zero_day_preparation': True,
                    'attack_vector_diversity': True,
                    'real_time_adaptation': True
                }
            }
            
        elif dataset_type == DatasetType.FINANCIAL:
            return {
                'curriculum_philosophy': 'risk_aware',
                'stage_transitions': {
                    'foundation_to_adversarial': 0.85,  # 极高标准
                    'adversarial_to_mastery': 0.92
                },
                'difficulty_progression': 'risk_graduated',
                'intervention_style': 'conservative_guidance',
                'feedback_frequency': 'moderate',
                'reward_structure': {
                    'precision_weight': 0.4,
                    'recall_weight': 0.3,
                    'regulatory_compliance_weight': 0.3
                },
                'special_considerations': {
                    'regulatory_constraints': True,
                    'cost_sensitive_learning': True,
                    'explainability_requirement': True
                }
            }
            
        elif dataset_type == DatasetType.MEDICAL:
            return {
                'curriculum_philosophy': 'safety_first',
                'stage_transitions': {
                    'foundation_to_adversarial': 0.9,   # 最高标准
                    'adversarial_to_mastery': 0.95
                },
                'difficulty_progression': 'clinical_severity',
                'intervention_style': 'expert_consultation',
                'feedback_frequency': 'comprehensive',
                'reward_structure': {
                    'sensitivity_weight': 0.5,
                    'specificity_weight': 0.3,
                    'clinical_relevance_weight': 0.2
                },
                'special_considerations': {
                    'patient_safety_priority': True,
                    'clinical_validation': True,
                    'ethical_constraints': True
                }
            }
            
        else:  # GENERAL
            return {
                'curriculum_philosophy': 'balanced_growth',
                'stage_transitions': {
                    'foundation_to_adversarial': 0.7,
                    'adversarial_to_mastery': 0.8
                },
                'difficulty_progression': 'linear_adaptive',
                'intervention_style': 'supportive',
                'feedback_frequency': 'moderate',
                'reward_structure': {
                    'accuracy_weight': 0.5,
                    'efficiency_weight': 0.3,
                    'robustness_weight': 0.2
                },
                'special_considerations': {}
            }
            
    def _adjust_for_battlefield_characteristics(self, 
                                              base_strategy: Dict[str, Any],
                                              characteristics: List[BattlefieldCharacteristics]) -> Dict[str, Any]:
        """根据战场特征调整策略"""
        
        adjusted_strategy = base_strategy.copy()
        
        for characteristic in characteristics:
            
            if characteristic == BattlefieldCharacteristics.HIGH_NOISE:
                # 高噪声环境：需要更多的重复和确认
                adjusted_strategy['feedback_frequency'] = 'very_high'
                adjusted_strategy['intervention_style'] = 'noise_robust'
                adjusted_strategy['stage_transitions']['foundation_to_adversarial'] += 0.05
                
            elif characteristic == BattlefieldCharacteristics.IMBALANCED:
                # 不平衡数据：调整奖励结构
                adjusted_strategy['reward_structure']['minority_class_bonus'] = 2.0
                adjusted_strategy['special_considerations']['class_balancing'] = True
                
            elif characteristic == BattlefieldCharacteristics.HIGH_DIMENSIONAL:
                # 高维特征：需要特征选择指导
                adjusted_strategy['intervention_style'] = 'feature_guidance'
                adjusted_strategy['special_considerations']['dimensionality_reduction'] = True
                
            elif characteristic == BattlefieldCharacteristics.TEMPORAL:
                # 时序特性：考虑时间依赖
                adjusted_strategy['difficulty_progression'] = 'temporal_aware'
                adjusted_strategy['special_considerations']['temporal_modeling'] = True
                
            elif characteristic == BattlefieldCharacteristics.SPARSE:
                # 稀疏特征：需要稀疏性感知策略
                adjusted_strategy['intervention_style'] = 'sparsity_aware'
                adjusted_strategy['special_considerations']['sparse_regularization'] = True
                
            elif characteristic == BattlefieldCharacteristics.ADVERSARIAL_PRONE:
                # 易受对抗攻击：增强防御训练
                adjusted_strategy['curriculum_philosophy'] = 'adversarial_hardening'
                adjusted_strategy['stage_transitions']['adversarial_to_mastery'] += 0.05
                
        return adjusted_strategy
        
    def _tune_for_statistical_properties(self,
                                       strategy: Dict[str, Any],
                                       profile: DatasetProfile) -> Dict[str, Any]:
        """根据统计特性微调策略"""
        
        tuned_strategy = strategy.copy()
        
        # 根据异常比例调整
        if profile.anomaly_ratio < 0.05:  # 极度不平衡
            tuned_strategy['reward_structure']['anomaly_detection_bonus'] = 3.0
            tuned_strategy['special_considerations']['extreme_imbalance_handling'] = True
            
        elif profile.anomaly_ratio > 0.4:  # 相对平衡
            tuned_strategy['reward_structure']['precision_weight'] = 0.6
            
        # 根据难度分布调整
        difficulty_std = profile.difficulty_distribution['std']
        if difficulty_std > 0.3:  # 难度分布很广
            tuned_strategy['difficulty_progression'] = 'wide_range_adaptive'
            tuned_strategy['stage_transitions']['foundation_to_adversarial'] += 0.1
            
        # 根据领域复杂度调整
        if profile.domain_complexity > 0.8:  # 高复杂度
            tuned_strategy['intervention_style'] = 'expert_intensive'
            tuned_strategy['feedback_frequency'] = 'very_high'
            
        # 根据对抗脆弱性调整
        if profile.adversarial_vulnerability > 0.7:  # 高脆弱性
            tuned_strategy['curriculum_philosophy'] = 'robustness_focused'
            tuned_strategy['special_considerations']['adversarial_training_emphasis'] = True
            
        return tuned_strategy
        
    def _adjust_for_game_state(self,
                             strategy: Dict[str, Any],
                             game_state: GameState,
                             profile: DatasetProfile) -> Dict[str, Any]:
        """根据当前博弈状态动态调整"""
        
        adjusted_strategy = strategy.copy()
        
        # 根据当前阶段调整
        current_stage = game_state.curriculum_stage
        
        if current_stage == 'foundation':
            # 基础阶段：重点建立信心
            if game_state.student_performance.get('detection_accuracy', 0) < 0.5:
                adjusted_strategy['intervention_style'] = 'confidence_building'
                adjusted_strategy['difficulty_progression'] = 'gentle_slope'
                
        elif current_stage == 'adversarial':
            # 对抗阶段：平衡挑战和支持
            adversary_success = game_state.adversary_success_rate
            if adversary_success > 0.7:  # 对手太强
                adjusted_strategy['intervention_style'] = 'defensive_support'
                adjusted_strategy['feedback_frequency'] = 'high'
            elif adversary_success < 0.3:  # 对手太弱
                adjusted_strategy['special_considerations']['adversary_enhancement'] = True
                
        elif current_stage == 'mastery':
            # 精通阶段：最小干预，最大挑战
            adjusted_strategy['intervention_style'] = 'minimal'
            adjusted_strategy['feedback_frequency'] = 'low'
            adjusted_strategy['special_considerations']['autonomous_learning'] = True
            
        return adjusted_strategy
        
    def _optimize_based_on_history(self,
                                 strategy: Dict[str, Any],
                                 performance_history: List[Dict],
                                 profile: DatasetProfile) -> Dict[str, Any]:
        """基于历史性能优化策略"""
        
        if not performance_history:
            return strategy
            
        optimized_strategy = strategy.copy()
        
        # 分析性能趋势
        recent_performance = performance_history[-10:] if len(performance_history) >= 10 else performance_history
        
        # 计算性能指标
        avg_accuracy = np.mean([p.get('detection_accuracy', 0) for p in recent_performance])
        avg_balance = np.mean([p.get('game_balance', 0.5) for p in recent_performance])
        performance_variance = np.var([p.get('detection_accuracy', 0) for p in recent_performance])
        
        # 根据性能调整策略
        if avg_accuracy < 0.6:  # 性能不佳
            optimized_strategy['intervention_style'] = 'intensive_support'
            optimized_strategy['feedback_frequency'] = 'very_high'
            
        elif avg_accuracy > 0.85:  # 性能优秀
            optimized_strategy['difficulty_progression'] = 'accelerated'
            optimized_strategy['special_considerations']['advanced_challenges'] = True
            
        if performance_variance > 0.1:  # 性能不稳定
            optimized_strategy['special_considerations']['stability_focus'] = True
            optimized_strategy['intervention_style'] = 'consistency_building'
            
        if abs(avg_balance - 0.5) > 0.2:  # 博弈不平衡
            optimized_strategy['special_considerations']['balance_restoration'] = True
            
        return optimized_strategy
        
    def _add_exploration_elements(self,
                                strategy: Dict[str, Any],
                                profile: DatasetProfile,
                                game_state: GameState) -> Dict[str, Any]:
        """添加探索性元素"""
        
        final_strategy = strategy.copy()
        
        # 添加随机探索
        exploration_rate = self.config.get('exploration_rate', 0.1)
        
        if np.random.random() < exploration_rate:
            # 随机调整某些参数
            if 'stage_transitions' in final_strategy:
                for key in final_strategy['stage_transitions']:
                    adjustment = np.random.normal(0, 0.02)  # 小幅随机调整
                    final_strategy['stage_transitions'][key] += adjustment
                    final_strategy['stage_transitions'][key] = np.clip(
                        final_strategy['stage_transitions'][key], 0.5, 0.98
                    )
                    
        # 添加创新性策略
        if game_state.round_number > 50:  # 训练后期
            final_strategy['special_considerations']['innovation_mode'] = True
            
        return final_strategy
        
    # 辅助方法
    def _identify_dataset_type(self, samples: List[Any]) -> DatasetType:
        """识别数据集类型"""
        # 简化实现：基于样本的domain字段
        if samples:
            domain = getattr(samples[0], 'domain', 'general')
            if 'knowledge' in domain.lower():
                return DatasetType.KNOWLEDGE_TRACING
            elif 'cyber' in domain.lower() or 'security' in domain.lower():
                return DatasetType.CYBERSECURITY
            elif 'financial' in domain.lower() or 'finance' in domain.lower():
                return DatasetType.FINANCIAL
            elif 'medical' in domain.lower() or 'health' in domain.lower():
                return DatasetType.MEDICAL
        return DatasetType.GENERAL
        
    def _identify_battlefield_characteristics(self, samples: List[Any]) -> List[BattlefieldCharacteristics]:
        """识别战场特征"""
        characteristics = []
        
        if not samples:
            return characteristics
            
        # 检查高维特征
        if hasattr(samples[0], 'features') and len(samples[0].features) > 50:
            characteristics.append(BattlefieldCharacteristics.HIGH_DIMENSIONAL)
            
        # 检查不平衡
        anomaly_ratio = sum(1 for s in samples if s.label == 1) / len(samples)
        if anomaly_ratio < 0.1 or anomaly_ratio > 0.9:
            characteristics.append(BattlefieldCharacteristics.IMBALANCED)
            
        # 检查稀疏性
        if hasattr(samples[0], 'features'):
            avg_sparsity = np.mean([(s.features == 0).float().mean().item() for s in samples[:100]])
            if avg_sparsity > 0.7:
                characteristics.append(BattlefieldCharacteristics.SPARSE)
                
        return characteristics
        
    def _calculate_domain_complexity(self, samples: List[Any]) -> float:
        """计算领域复杂度"""
        if not samples:
            return 0.5
            
        # 基于特征维度和难度分布
        feature_dim = len(samples[0].features) if hasattr(samples[0], 'features') else 10
        difficulties = [s.difficulty for s in samples]
        
        complexity = min(1.0, (feature_dim / 100) * 0.5 + np.std(difficulties) * 0.5)
        return complexity
        
    def _estimate_noise_level(self, samples: List[Any]) -> float:
        """估计噪声水平"""
        # 简化实现：基于特征方差
        if not samples or not hasattr(samples[0], 'features'):
            return 0.1
            
        feature_vars = []
        for i in range(len(samples[0].features)):
            feature_values = [s.features[i].item() for s in samples[:100]]
            feature_vars.append(np.var(feature_values))
            
        avg_var = np.mean(feature_vars)
        return min(1.0, avg_var / 2.0)
        
    def _assess_adversarial_vulnerability(self, samples: List[Any]) -> float:
        """评估对抗脆弱性"""
        # 简化实现：基于数据集类型和特征
        if not samples:
            return 0.5
            
        dataset_type = self._identify_dataset_type(samples)
        
        vulnerability_map = {
            DatasetType.CYBERSECURITY: 0.8,
            DatasetType.FINANCIAL: 0.7,
            DatasetType.KNOWLEDGE_TRACING: 0.4,
            DatasetType.MEDICAL: 0.6,
            DatasetType.GENERAL: 0.5
        }
        
        return vulnerability_map.get(dataset_type, 0.5)
        
    def _estimate_learning_steepness(self, dataset_type: DatasetType, characteristics: List) -> float:
        """估计学习曲线陡峭度"""
        base_steepness = {
            DatasetType.KNOWLEDGE_TRACING: 0.6,
            DatasetType.CYBERSECURITY: 0.8,
            DatasetType.FINANCIAL: 0.4,
            DatasetType.MEDICAL: 0.3,
            DatasetType.GENERAL: 0.5
        }
        
        steepness = base_steepness.get(dataset_type, 0.5)
        
        # 根据特征调整
        if BattlefieldCharacteristics.HIGH_DIMENSIONAL in characteristics:
            steepness *= 0.8
        if BattlefieldCharacteristics.HIGH_NOISE in characteristics:
            steepness *= 0.7
            
        return steepness
        
    def _estimate_plateau_tendency(self, dataset_type: DatasetType, difficulty_dist: Dict) -> float:
        """估计平台期倾向"""
        base_tendency = {
            DatasetType.KNOWLEDGE_TRACING: 0.7,
            DatasetType.CYBERSECURITY: 0.3,
            DatasetType.FINANCIAL: 0.8,
            DatasetType.MEDICAL: 0.9,
            DatasetType.GENERAL: 0.5
        }
        
        tendency = base_tendency.get(dataset_type, 0.5)
        
        # 根据难度分布调整
        if difficulty_dist['std'] > 0.3:
            tendency *= 0.8  # 难度分布广，平台期倾向低
            
        return tendency
        
    def _estimate_forgetting_rate(self, dataset_type: DatasetType, complexity: float) -> float:
        """估计遗忘率"""
        base_rate = {
            DatasetType.KNOWLEDGE_TRACING: 0.6,
            DatasetType.CYBERSECURITY: 0.2,
            DatasetType.FINANCIAL: 0.3,
            DatasetType.MEDICAL: 0.4,
            DatasetType.GENERAL: 0.4
        }
        
        rate = base_rate.get(dataset_type, 0.4)
        
        # 复杂度越高，遗忘率越高
        rate += complexity * 0.2
        
        return min(1.0, rate)
        
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0
        return float(np.mean([(x - np.mean(data))**3 for x in data]) / (np.std(data)**3))
        
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0
        return float(np.mean([(x - np.mean(data))**4 for x in data]) / (np.std(data)**4) - 3)
        
    def _load_strategy_templates(self) -> Dict[str, Any]:
        """加载策略模板"""
        # 这里可以从配置文件加载
        return {
            'default_exploration_rate': 0.1,
            'adaptation_sensitivity': 0.05,
            'performance_window': 10
        }
