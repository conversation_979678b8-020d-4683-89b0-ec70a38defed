"""
第2阶段：异常检测器训练

训练专门的异常检测器，用于识别学习者的异常答题行为。
支持多种训练策略：Basic, Enhanced, Aggressive
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Any
from DTransformer.data import KTData
from ..detector import CausalAnomalyDetector
from ..detector_strategies import DetectorStrategyFactory
# 导入博弈论框架
from ...game_theory.game_agents import TeacherAgent, GameState


class DetectorTrainingStage:
    """第2阶段：异常检测器训练"""

    def __init__(self, args: Any, device: str, output_dir: str):
        """
        初始化异常检测器训练阶段

        Args:
            args: 命令行参数
            device: 设备 (cuda/cpu)
            output_dir: 输出目录
        """
        self.args = args
        self.device = device
        self.output_dir = output_dir
        self.save_dir = os.path.join(output_dir, 'detector')
        os.makedirs(self.save_dir, exist_ok=True)

        # 初始化教师智能体用于课程策略指导
        self.teacher_agent = None
        self.dataset_profile = None

    def execute(self, dataset_config: Dict, train_data: KTData, val_data: KTData) -> str:
        """
        执行异常检测器训练

        Args:
            dataset_config: 数据集配置
            train_data: 训练数据
            val_data: 验证数据

        Returns:
            str: 保存的检测器模型路径
        """
        print("\n" + "="*60)
        print("STAGE 2: Training Anomaly Detector")
        print("="*60)
        print("🎯 目标: 训练高质量的异常检测器")
        print("🔍 功能: 识别学习者的异常答题行为")

        # 初始化教师智能体和数据集感知课程策略
        self._initialize_teacher_agent(dataset_config)

        # 创建异常检测器
        detector = self._create_detector(dataset_config)
        print(f"🧠 检测器参数: {sum(p.numel() for p in detector.parameters()):,}")

        # 显示训练策略信息
        self._display_strategy_info()

        # 创建训练器（集成课程策略）
        trainer = self._create_trainer_with_curriculum(detector)

        # 执行训练
        detector_metrics = self._train_detector(trainer, train_data, val_data)

        # 保存结果
        model_path = self._save_results(detector_metrics)

        print(f"\n✅ Stage 2 完成!")
        print(f"📈 最佳性能: {detector_metrics.get('best_score', 'N/A')}")
        print(f"💾 检测器保存至: {model_path}")

        return model_path

    def _create_detector(self, dataset_config: Dict) -> nn.Module:
        """创建异常检测器模型"""
        detector = CausalAnomalyDetector(
            n_questions=dataset_config['n_questions'],
            n_pid=dataset_config['n_pid'] if self.args.with_pid else 0,
            d_model=getattr(self.args, 'detector_d_model', self.args.d_model),
            n_heads=getattr(self.args, 'detector_n_heads', self.args.n_heads),
            n_layers=getattr(self.args, 'detector_n_layers', 2),
            dropout=getattr(self.args, 'detector_dropout', self.args.dropout),
            window_size=getattr(self.args, 'window_size', 10)
        )
        return detector.to(self.device)

    def _initialize_teacher_agent(self, dataset_config: Dict):
        """初始化教师智能体和数据集画像"""
        # 创建数据集画像
        self.dataset_profile = {
            'dataset_name': getattr(self.args, 'dataset', 'unknown'),
            'n_questions': dataset_config['n_questions'],
            'n_pid': dataset_config['n_pid'],
            'domain_focus': self._infer_domain_focus(dataset_config),
            'complexity': self._infer_complexity(dataset_config),
            'scale': self._infer_scale(dataset_config)
        }

        # 创建教师智能体
        teacher_config = {
            'learning_rate': 0.01,
            'stage_thresholds': {
                'foundation_to_adversarial': 0.7,
                'adversarial_to_mastery': 0.85
            }
        }
        self.teacher_agent = TeacherAgent("detector_teacher", teacher_config)

        print(f"🎓 教师智能体已初始化")
        print(f"📊 数据集画像: {self.dataset_profile['dataset_name']} - {self.dataset_profile['domain_focus']}")

    def _infer_domain_focus(self, dataset_config: Dict) -> str:
        """推断数据集的领域焦点"""
        dataset_name = getattr(self.args, 'dataset', 'unknown').lower()
        if 'assist' in dataset_name:
            return 'mathematics_problem_solving'
        elif 'algebra' in dataset_name:
            return 'algebra_mathematics'
        elif 'statics' in dataset_name:
            return 'physics_statics'
        else:
            return 'general_knowledge_tracing'

    def _infer_complexity(self, dataset_config: Dict) -> str:
        """推断数据集复杂度"""
        n_questions = dataset_config['n_questions']
        n_pid = dataset_config['n_pid']

        if n_questions > 1000 or n_pid > 5000:
            return 'high'
        elif n_questions > 100 or n_pid > 1000:
            return 'moderate'
        else:
            return 'low'

    def _infer_scale(self, dataset_config: Dict) -> str:
        """推断数据集规模"""
        n_questions = dataset_config['n_questions']

        if n_questions > 1000:
            return 'large'
        elif n_questions > 100:
            return 'medium'
        else:
            return 'small'

    def _display_strategy_info(self):
        """显示训练策略信息"""
        strategy = getattr(self.args, 'training_strategy', 'basic')
        print(f"📋 训练策略: {strategy.upper()}")

        strategy_descriptions = {
            'basic': '基础策略 - 标准训练流程',
            'enhanced': '增强策略 - 课程学习 + 动态调整',
            'aggressive': '激进策略 - 极端不平衡处理'
        }

        print(f"📝 策略描述: {strategy_descriptions.get(strategy, '未知策略')}")
        print(f"🎯 优化目标: {getattr(self.args, 'optimize_for', 'f1_score')}")
        print(f"⚖️  异常比例: {getattr(self.args, 'anomaly_ratio', 0.1)}")

    def _create_trainer(self, detector: nn.Module):
        """创建策略训练器"""
        strategy_name = getattr(self.args, 'training_strategy', 'basic')

        # 使用策略工厂创建训练策略
        trainer = DetectorStrategyFactory.create_strategy(
            strategy_name=strategy_name,
            model=detector,
            device=self.device,
            save_dir=self.save_dir,
            patience=getattr(self.args, 'detector_patience', self.args.patience)
        )

        # 显示策略信息
        strategy_info = DetectorStrategyFactory.get_strategy_info(strategy_name)
        print(f"✓ 使用策略: {strategy_info.get('name', strategy_name)}")
        print(f"✓ 策略特性: {', '.join(strategy_info.get('features', []))}")

        return trainer

    def _create_trainer_with_curriculum(self, detector: nn.Module):
        """创建集成课程策略的训练器"""
        strategy_name = getattr(self.args, 'training_strategy', 'basic')

        # 如果使用enhanced策略，集成教师智能体的课程策略
        if strategy_name == 'enhanced' and self.teacher_agent is not None:
            print("🎓 集成教师智能体的数据集感知课程策略")

            # 创建初始博弈状态
            game_state = GameState(
                round_number=0,
                student_performance={'detection_accuracy': 0.5},
                adversary_success_rate=0.5,
                curriculum_stage='foundation',
                difficulty_level=0.5,
                historical_outcomes=[]
            )

            # 获取数据集特定的课程策略
            curriculum_strategy = self.teacher_agent.design_dataset_specific_curriculum(
                self.dataset_profile, game_state
            )

            print(f"📚 课程哲学: {curriculum_strategy['curriculum_philosophy']}")
            print(f"🎯 领域焦点: {curriculum_strategy['domain_focus']}")

            # 显示当前阶段的策略
            foundation_stage = curriculum_strategy['stage_design']['foundation']
            print(f"🔍 基础阶段焦点: {foundation_stage['focus']}")
            print(f"🎭 目标异常类型: {foundation_stage['anomaly_types']}")
            print(f"📈 难度递增策略: {foundation_stage['difficulty_progression']}")
            print(f"🎯 掌握阈值: {foundation_stage['mastery_threshold']:.2f}")

        # 使用策略工厂创建训练策略
        trainer = DetectorStrategyFactory.create_strategy(
            strategy_name=strategy_name,
            model=detector,
            device=self.device,
            save_dir=self.save_dir,
            patience=getattr(self.args, 'detector_patience', self.args.patience)
        )

        # 显示策略信息
        strategy_info = DetectorStrategyFactory.get_strategy_info(strategy_name)
        print(f"✓ 使用策略: {strategy_info.get('name', strategy_name)}")
        print(f"✓ 策略特性: {', '.join(strategy_info.get('features', []))}")

        return trainer

    def _train_detector(self, trainer, train_data: KTData, val_data: KTData) -> Dict:
        """执行检测器训练"""
        print(f"\n🚀 开始异常检测器训练...")
        print(f"📊 训练轮数: {getattr(self.args, 'detector_epochs', 30)}")
        print(f"📈 学习率: {getattr(self.args, 'detector_lr', self.args.learning_rate)}")

        return trainer.train(
            train_loader=train_data,
            val_loader=val_data,
            epochs=getattr(self.args, 'detector_epochs', 30),
            learning_rate=getattr(self.args, 'detector_lr', self.args.learning_rate),
            anomaly_ratio=getattr(self.args, 'anomaly_ratio', 0.1),
            optimize_for=getattr(self.args, 'optimize_for', 'f1_score')
        )

    def _save_results(self, metrics: Dict) -> str:
        """保存训练结果"""
        # 保存训练指标
        import json
        metrics_path = os.path.join(self.save_dir, 'detector_metrics.json')
        with open(metrics_path, 'w') as f:
            json.dump(metrics, f, indent=2)

        # 返回最佳模型路径
        return os.path.join(self.save_dir, 'best_model.pt')

    def get_stage_info(self) -> Dict:
        """获取阶段信息"""
        available_strategies = DetectorStrategyFactory.get_available_strategies()

        return {
            'stage_number': 2,
            'stage_name': 'detector_training',
            'description': '异常检测器训练',
            'purpose': '训练高质量的异常检测器',
            'output': '训练好的异常检测器模型',
            'dependencies': [],
            'next_stage': 'anomaly_aware_training',
            'supported_strategies': available_strategies,
            'strategy_details': {
                strategy: DetectorStrategyFactory.get_strategy_info(strategy)
                for strategy in available_strategies
            }
        }
