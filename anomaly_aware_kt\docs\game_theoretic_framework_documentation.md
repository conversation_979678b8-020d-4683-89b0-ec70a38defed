# 博弈论异常检测训练框架 - 完整文档

## 📋 目录

1. [框架概述](#框架概述)
2. [理论基础](#理论基础)
3. [架构设计](#架构设计)
4. [核心组件](#核心组件)
5. [使用指南](#使用指南)
6. [配置说明](#配置说明)
7. [API参考](#api参考)
8. [示例代码](#示例代码)
9. [性能分析](#性能分析)
10. [故障排除](#故障排除)

## 🎯 框架概述

### 核心理念

博弈论异常检测训练框架将异常检测问题建模为**教师-学生-对手**的三方博弈，通过不完全信息博弈和自适应课程学习，实现更加鲁棒和智能的异常检测器训练。

### 主要特性

- ✅ **三方博弈模型**: 教师协调、学生学习、对手挑战
- ✅ **不完全信息博弈**: 真实模拟实际对抗环境
- ✅ **自适应课程学习**: 动态调整训练难度和策略
- ✅ **纳什均衡收敛**: 理论保证训练稳定性
- ✅ **多智能体强化学习**: 持续适应和改进
- ✅ **可扩展架构**: 支持多种异常检测场景

### 应用场景

| 领域 | 应用 | 学生(检测器) | 对手(生成器) | 教师(协调者) |
|------|------|-------------|-------------|-------------|
| **知识追踪** | 学习行为异常检测 | 异常行为检测模型 | 学习行为异常模拟器 | 课程设计优化器 |
| **网络安全** | 入侵检测 | 入侵检测系统 | 攻击模拟器 | 安全策略制定者 |
| **金融风控** | 欺诈检测 | 欺诈检测模型 | 欺诈行为模拟器 | 风控策略优化器 |
| **医疗诊断** | 异常症状识别 | 诊断模型 | 症状变异模拟器 | 诊断策略优化器 |

## 🔬 理论基础

### 博弈论数学模型

#### 三方博弈定义
设博弈 $G = (N, S, U, I)$，其中：
- $N = \{Teacher, Student, Adversary\}$ 为参与者集合
- $S = S_T \times S_S \times S_A$ 为策略空间
- $U = (u_T, u_S, u_A)$ 为效用函数
- $I = (I_T, I_S, I_A)$ 为信息结构

#### 效用函数设计

**教师效用函数**:
$$u_T = \alpha \cdot \text{StudentProgress} + \beta \cdot \text{CurriculumEfficiency} - \gamma \cdot \text{TrainingCost}$$

**学生效用函数**:
$$u_S = \text{DetectionAccuracy} - \lambda \cdot \text{FalsePositiveRate}$$

**对手效用函数**:
$$u_A = \text{EvasionRate} - \mu \cdot \text{GenerationCost}$$

#### 信息结构

- **教师信息**: $I_T = \{θ_S, θ_A, H_t\}$ (学生能力、对手能力、历史记录)
- **学生信息**: $I_S = \{X_t, Y_t, F_t\}$ (当前输入、标签、反馈)
- **对手信息**: $I_A = \{X_t, D_t\}$ (输入数据、检测结果)

### 课程学习理论

#### 自适应课程函数
$$C_t = f(P_{S,t}, P_{A,t}, D_t)$$

其中：
- $P_{S,t}$: 学生在时刻$t$的性能
- $P_{A,t}$: 对手在时刻$t$的成功率
- $D_t$: 当前难度级别

#### 三阶段课程设计

1. **基础阶段 (Foundation)**
   - 目标：建立基本检测能力
   - 对手策略：简单、可预测的异常
   - 教师策略：提供充分反馈，低惩罚

2. **对抗阶段 (Adversarial)**
   - 目标：提高鲁棒性
   - 对手策略：自适应、欺骗性异常
   - 教师策略：平衡奖惩，引入不确定性

3. **精通阶段 (Mastery)**
   - 目标：达到专家水平
   - 对手策略：高度复杂、创新性异常
   - 教师策略：最小干预，自主学习

## 🏗️ 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    博弈论训练框架                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ TeacherAgent│  │StudentAgent │  │AdversaryAgent│          │
│  │   (教师)     │  │   (学生)     │  │   (对手)     │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │GameEnvironment│ │CurriculumDesigner│ │NashSolver│          │
│  │   (博弈环境)   │  │  (课程设计器)  │  │ (均衡求解) │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            GameTheoreticTrainer                         │ │
│  │              (博弈训练器)                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 数据流图

```
输入数据 → 博弈环境 → 智能体策略选择 → 博弈执行 → 结果评估 → 信念更新 → 策略调整
    ↑                                                                    ↓
    └─────────────────── 课程调整 ← 教师协调 ← 性能分析 ←─────────────────────┘
```

## 🧩 核心组件

### 1. 智能体系统

#### TeacherAgent (教师智能体)
- **职责**: 课程设计、难度调节、训练协调
- **核心方法**:
  - `select_strategy()`: 选择教学策略
  - `adjust_difficulty()`: 调整难度级别
  - `design_reward_structure()`: 设计奖惩机制
  - `select_intervention()`: 选择干预策略

#### StudentAgent (学生智能体)
- **职责**: 学习异常检测策略，适应对手挑战
- **核心方法**:
  - `select_strategy()`: 选择检测策略
  - `predict_adversary_strategy()`: 预测对手策略
  - `compute_best_response()`: 计算最佳响应
  - `update_beliefs()`: 更新对手信念

#### AdversaryAgent (对手智能体)
- **职责**: 生成挑战性异常，测试检测器鲁棒性
- **核心方法**:
  - `select_strategy()`: 选择对抗策略
  - `analyze_student_detector()`: 分析检测器弱点
  - `compute_adversarial_strategy()`: 计算对抗策略
  - `add_creativity()`: 添加创新性变异

### 2. 博弈环境

#### GameEnvironment
- **职责**: 执行博弈轮次，计算结果
- **核心功能**:
  - 策略执行和结果计算
  - 性能指标统计
  - 博弈状态管理

### 3. 课程设计器

#### CurriculumDesigner
- **职责**: 设计和调整学习课程
- **核心功能**:
  - 阶段转换判断
  - 难度自适应调节
  - 学习进度评估

### 4. 均衡求解器

#### NashEquilibriumSolver
- **职责**: 求解纳什均衡点
- **核心功能**:
  - 迭代最佳响应算法
  - 收敛性检测
  - 均衡稳定性分析

## 📖 使用指南

### 快速开始

#### 1. 安装依赖
```bash
pip install torch numpy pyyaml
```

#### 2. 基本使用
```python
from game_theory import GameTheoreticTrainer, TrainingConfig

# 创建训练配置
config = TrainingConfig(
    max_episodes=1000,
    max_rounds_per_episode=50,
    convergence_threshold=0.01
)

# 配置智能体
teacher_config = {
    'learning_rate': 0.01,
    'stage_thresholds': {
        'foundation_to_adversarial': 0.7,
        'adversarial_to_mastery': 0.8
    }
}

student_config = {
    'initial_threshold': 0.5,
    'feature_dim': 10,
    'adaptation_rate': 0.1
}

adversary_config = {
    'anomaly_types': ['stealth', 'aggressive', 'adaptive'],
    'intensity_range': (0.1, 1.0),
    'creativity_factor': 0.2
}

# 创建训练器
trainer = GameTheoreticTrainer(
    config=config,
    teacher_config=teacher_config,
    student_config=student_config,
    adversary_config=adversary_config
)

# 执行训练
results = trainer.train()
```

### 高级配置

#### 自定义智能体策略
```python
class CustomStudentAgent(StudentAgent):
    def select_strategy(self, game_state):
        # 自定义策略选择逻辑
        strategy = super().select_strategy(game_state)

        # 添加自定义逻辑
        if game_state.curriculum_stage == 'mastery':
            strategy['detection_threshold'] *= 0.9

        return strategy
```

#### 自定义课程设计
```python
class CustomCurriculumDesigner(CurriculumDesigner):
    def design_curriculum(self, student_performance, adversary_strength):
        # 自定义课程设计逻辑
        curriculum = super().design_curriculum(student_performance, adversary_strength)

        # 添加特殊阶段
        if student_performance > 0.9:
            curriculum.add_stage('expert', difficulty=0.95)

        return curriculum
```

## ⚙️ 配置说明

### 训练配置 (TrainingConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_episodes` | int | 1000 | 最大训练回合数 |
| `max_rounds_per_episode` | int | 50 | 每回合最大轮次数 |
| `convergence_threshold` | float | 0.01 | 收敛阈值 |
| `performance_window` | int | 20 | 性能评估窗口大小 |
| `save_interval` | int | 100 | 保存间隔 |
| `log_interval` | int | 10 | 日志间隔 |

### 教师配置 (TeacherConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `learning_rate` | float | 0.01 | 学习率 |
| `difficulty_adjustment_rate` | float | 0.1 | 难度调整率 |
| `stage_thresholds` | dict | - | 阶段转换阈值 |
| `patience_threshold` | int | 5 | 耐心阈值 |

### 学生配置 (StudentConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `initial_threshold` | float | 0.5 | 初始检测阈值 |
| `feature_dim` | int | 10 | 特征维度 |
| `adaptation_rate` | float | 0.1 | 适应率 |
| `exploration_rate` | float | 0.1 | 探索率 |

### 对手配置 (AdversaryConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `anomaly_types` | list | ['stealth', 'aggressive', 'adaptive'] | 异常类型 |
| `intensity_range` | tuple | (0.1, 1.0) | 强度范围 |
| `creativity_factor` | float | 0.2 | 创新因子 |
| `adaptation_speed` | float | 0.15 | 适应速度 |

## 📚 API参考

### GameTheoreticTrainer

#### 构造函数
```python
GameTheoreticTrainer(
    config: TrainingConfig,
    teacher_config: Dict[str, Any],
    student_config: Dict[str, Any],
    adversary_config: Dict[str, Any],
    device: str = 'cpu'
)
```

#### 主要方法

##### train()
```python
def train() -> Dict[str, Any]:
    """
    执行博弈训练

    Returns:
        Dict[str, Any]: 训练结果和统计信息
            - total_episodes: 总训练回合数
            - convergence_achieved: 是否收敛
            - final_performance: 最终性能指标
            - agent_final_states: 智能体最终状态
            - training_statistics: 训练统计信息
    """
```

##### _train_episode()
```python
def _train_episode() -> List[GameResult]:
    """
    训练一个回合

    Returns:
        List[GameResult]: 回合结果列表
    """
```

##### _execute_game_round()
```python
def _execute_game_round(game_state: GameState, round_num: int) -> GameResult:
    """
    执行一轮博弈

    Args:
        game_state: 当前博弈状态
        round_num: 轮次编号

    Returns:
        GameResult: 博弈结果
    """
```

### BaseGameAgent

#### 抽象方法

##### select_strategy()
```python
@abstractmethod
def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
    """
    选择策略

    Args:
        game_state: 当前博弈状态

    Returns:
        Dict[str, Any]: 选择的策略
    """
```

##### update_beliefs()
```python
@abstractmethod
def update_beliefs(self, game_state: GameState, outcomes: Dict[str, Any]):
    """
    更新信念

    Args:
        game_state: 当前博弈状态
        outcomes: 博弈结果
    """
```

## 💡 示例代码

### 完整训练示例

```python
import torch
import numpy as np
from game_theory import (
    GameTheoreticTrainer,
    TrainingConfig,
    TeacherAgent,
    StudentAgent,
    AdversaryAgent
)

def main():
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    # 创建训练配置
    config = TrainingConfig(
        max_episodes=500,
        max_rounds_per_episode=30,
        convergence_threshold=0.005,
        performance_window=15,
        save_interval=50,
        log_interval=5
    )

    # 配置智能体
    teacher_config = {
        'learning_rate': 0.02,
        'difficulty_adjustment_rate': 0.08,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.75,
            'adversarial_to_mastery': 0.85
        },
        'patience_threshold': 3
    }

    student_config = {
        'initial_threshold': 0.6,
        'feature_dim': 15,
        'adaptation_rate': 0.12,
        'exploration_rate': 0.15,
        'memory_size': 200
    }

    adversary_config = {
        'anomaly_types': ['stealth', 'aggressive', 'adaptive', 'deceptive'],
        'intensity_range': (0.05, 0.95),
        'creativity_factor': 0.25,
        'adaptation_speed': 0.18
    }

    # 创建训练器
    trainer = GameTheoreticTrainer(
        config=config,
        teacher_config=teacher_config,
        student_config=student_config,
        adversary_config=adversary_config,
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )

    # 执行训练
    print("🚀 开始博弈论异常检测训练...")
    results = trainer.train()

    # 输出结果
    print("\n📊 训练完成！")
    print(f"总回合数: {results['total_episodes']}")
    print(f"是否收敛: {results['convergence_achieved']}")
    print(f"最终性能: {results['final_performance']}")

    # 保存结果
    torch.save(results, 'game_training_results.pt')
    print("💾 结果已保存到 game_training_results.pt")

if __name__ == "__main__":
    main()
```

### 自定义智能体示例

```python
class EnhancedStudentAgent(StudentAgent):
    """增强的学生智能体"""

    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)

        # 添加记忆网络
        self.memory_network = self._build_memory_network()

    def _build_memory_network(self):
        """构建记忆网络"""
        return torch.nn.LSTM(
            input_size=self.config.get('feature_dim', 10),
            hidden_size=64,
            num_layers=2,
            batch_first=True
        )

    def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """使用记忆网络增强的策略选择"""

        # 基础策略
        base_strategy = super().select_strategy(game_state)

        # 使用记忆网络预测
        if len(self.strategy_history) > 5:
            # 准备历史数据
            history_tensor = self._prepare_history_tensor()

            # 记忆网络预测
            with torch.no_grad():
                memory_output, _ = self.memory_network(history_tensor)
                memory_prediction = memory_output[:, -1, :]

            # 融合预测结果
            base_strategy = self._fuse_predictions(base_strategy, memory_prediction)

        return base_strategy

    def _prepare_history_tensor(self) -> torch.Tensor:
        """准备历史数据张量"""
        # 简化实现
        history_data = []
        for strategy in self.strategy_history[-5:]:
            # 将策略转换为向量
            vector = torch.cat([
                torch.tensor([strategy.get('detection_threshold', 0.5)]),
                strategy.get('feature_weights', torch.ones(9))
            ])
            history_data.append(vector)

        return torch.stack(history_data).unsqueeze(0)

    def _fuse_predictions(self, base_strategy: Dict[str, Any],
                         memory_prediction: torch.Tensor) -> Dict[str, Any]:
        """融合基础策略和记忆预测"""

        # 简单的加权融合
        fusion_weight = 0.3

        # 调整检测阈值
        memory_threshold = torch.sigmoid(memory_prediction[0]).item()
        base_strategy['detection_threshold'] = (
            (1 - fusion_weight) * base_strategy['detection_threshold'] +
            fusion_weight * memory_threshold
        )

        return base_strategy
```

### 性能监控示例

```python
class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics_history = defaultdict(list)

    def update_metrics(self, results: List[GameResult]):
        """更新性能指标"""
        for result in results:
            for key, value in result.performance_metrics.items():
                self.metrics_history[key].append(value)

    def plot_performance(self):
        """绘制性能曲线"""
        import matplotlib.pyplot as plt

        fig, axes = plt.subplots(2, 2, figsize=(12, 8))

        # 学生性能
        axes[0, 0].plot(self.metrics_history['student_performance'])
        axes[0, 0].set_title('Student Performance')
        axes[0, 0].set_ylabel('Performance')

        # 对手性能
        axes[0, 1].plot(self.metrics_history['adversary_performance'])
        axes[0, 1].set_title('Adversary Performance')
        axes[0, 1].set_ylabel('Performance')

        # 教师有效性
        axes[1, 0].plot(self.metrics_history['teacher_effectiveness'])
        axes[1, 0].set_title('Teacher Effectiveness')
        axes[1, 0].set_ylabel('Effectiveness')

        # 整体质量
        axes[1, 1].plot(self.metrics_history['overall_quality'])
        axes[1, 1].set_title('Overall Quality')
        axes[1, 1].set_ylabel('Quality')

        plt.tight_layout()
        plt.savefig('performance_curves.png')
        plt.show()

    def generate_report(self) -> str:
        """生成性能报告"""
        report = "📊 博弈训练性能报告\n"
        report += "=" * 50 + "\n\n"

        for metric, values in self.metrics_history.items():
            if values:
                report += f"{metric}:\n"
                report += f"  平均值: {np.mean(values):.4f}\n"
                report += f"  标准差: {np.std(values):.4f}\n"
                report += f"  最终值: {np.mean(values[-10:]):.4f}\n\n"

        return report
```

## 📈 性能分析

### 收敛性分析

框架提供多种收敛性指标：

1. **性能方差**: 监控最近N轮的性能方差
2. **策略稳定性**: 检测策略变化的频率
3. **博弈平衡**: 评估学生-对手的势均力敌程度

### 性能基准

| 指标 | 优秀 | 良好 | 一般 | 需改进 |
|------|------|------|------|--------|
| 学生检测准确率 | >0.90 | 0.80-0.90 | 0.70-0.80 | <0.70 |
| 对手逃避成功率 | 0.40-0.60 | 0.30-0.70 | 0.20-0.80 | <0.20 或 >0.80 |
| 博弈平衡度 | >0.80 | 0.70-0.80 | 0.60-0.70 | <0.60 |
| 收敛速度 | <100轮 | 100-300轮 | 300-500轮 | >500轮 |

### 优化建议

1. **学习率调优**: 根据收敛速度调整各智能体学习率
2. **探索-利用平衡**: 动态调整探索率
3. **课程设计**: 优化阶段转换阈值
4. **奖励函数**: 调整效用函数权重

## 🔧 故障排除

### 常见问题

#### 1. 训练不收敛
**症状**: 性能指标持续波动，无法达到收敛阈值

**可能原因**:
- 学习率过高
- 探索率过高
- 课程设计不合理

**解决方案**:
```python
# 降低学习率
config['learning_rate'] = 0.005

# 减少探索率
config['exploration_rate'] = 0.05

# 调整收敛阈值
training_config.convergence_threshold = 0.02
```

#### 2. 博弈不平衡
**症状**: 学生或对手一方过于强势

**可能原因**:
- 智能体配置不当
- 奖励函数设计问题

**解决方案**:
```python
# 调整对手创新性
adversary_config['creativity_factor'] = 0.15

# 调整学生适应率
student_config['adaptation_rate'] = 0.08
```

#### 3. 内存使用过高
**症状**: 训练过程中内存持续增长

**可能原因**:
- 历史记录过长
- 批处理大小过大

**解决方案**:
```python
# 限制历史记录长度
config['max_history_length'] = 100

# 定期清理缓存
trainer.clear_cache()
```

### 调试工具

#### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 性能分析
```python
import cProfile
cProfile.run('trainer.train()', 'profile_results.prof')
```

#### 可视化调试
```python
trainer.enable_visualization = True
trainer.save_debug_plots = True
```

## 📞 技术支持

### 联系方式
- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [项目仓库](https://github.com/anomaly-detection/game-theory)
- 📖 文档: [在线文档](https://docs.anomaly-detection.ai)

### 贡献指南
欢迎提交Pull Request和Issue！请参考[贡献指南](CONTRIBUTING.md)。

---

*本文档版本: v1.0.0 | 最后更新: 2024年*
