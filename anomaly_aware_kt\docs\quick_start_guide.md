# 🚀 博弈论异常检测框架 - 快速入门指南

## 📋 5分钟快速上手

### 1️⃣ 安装环境

```bash
# 克隆项目
git clone https://github.com/your-repo/anomaly-aware-kt.git
cd anomaly-aware-kt

# 安装依赖
pip install torch numpy pyyaml matplotlib seaborn
```

### 2️⃣ 基础示例

```python
from game_theory import GameTheoreticTrainer, TrainingConfig

# 🎯 第一步：创建配置
config = TrainingConfig(
    max_episodes=100,           # 训练100个回合
    max_rounds_per_episode=20,  # 每回合20轮
    convergence_threshold=0.01  # 收敛阈值
)

# 🎭 第二步：配置三方智能体
teacher_config = {
    'learning_rate': 0.01,
    'stage_thresholds': {'foundation_to_adversarial': 0.7}
}

student_config = {
    'initial_threshold': 0.5,
    'feature_dim': 10,
    'adaptation_rate': 0.1
}

adversary_config = {
    'anomaly_types': ['stealth', 'aggressive'],
    'intensity_range': (0.1, 0.8),
    'creativity_factor': 0.2
}

# 🎮 第三步：创建训练器并开始训练
trainer = GameTheoreticTrainer(
    config=config,
    teacher_config=teacher_config,
    student_config=student_config,
    adversary_config=adversary_config
)

# 🚀 开始训练！
results = trainer.train()

# 📊 查看结果
print(f"训练完成！收敛状态: {results['convergence_achieved']}")
print(f"最终性能: {results['final_performance']['overall_quality']:.3f}")
```

### 3️⃣ 运行结果示例

```
🎮 开始博弈论异常检测训练
📊 配置: 100 episodes, 20 rounds/episode

📊 Episode 0:
   整体质量: 0.456
   学生表现: 0.423
   对手表现: 0.512
   教师有效性: 0.434

📊 Episode 10:
   整体质量: 0.623
   学生表现: 0.645
   对手表现: 0.578
   教师有效性: 0.646

...

🎯 训练在第 67 回合收敛
✅ 博弈训练完成

训练完成！收敛状态: True
最终性能: 0.847
```

## 🎯 核心概念速览

### 三方博弈角色

| 角色 | 职责 | 目标 |
|------|------|------|
| 🎓 **教师** | 课程设计、训练协调 | 最大化学生学习效果 |
| 🎯 **学生** | 异常检测学习 | 最大化检测准确率 |
| 👹 **对手** | 异常生成挑战 | 最大化逃避检测率 |

### 训练流程

```
1. 教师制定课程策略 → 2. 学生选择检测策略 → 3. 对手选择攻击策略
                    ↓
6. 更新智能体信念 ← 5. 计算性能指标 ← 4. 执行博弈并评估结果
```

## 🛠️ 常用配置模板

### 🎯 知识追踪场景
```python
# 适用于学习行为异常检测
knowledge_tracing_config = {
    'teacher_config': {
        'learning_rate': 0.02,
        'difficulty_adjustment_rate': 0.1,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.75,
            'adversarial_to_mastery': 0.85
        }
    },
    'student_config': {
        'initial_threshold': 0.6,
        'feature_dim': 15,  # 学习特征维度
        'adaptation_rate': 0.12
    },
    'adversary_config': {
        'anomaly_types': ['cognitive_load', 'metacognitive', 'motivational'],
        'intensity_range': (0.1, 0.9),
        'creativity_factor': 0.25
    }
}
```

### 🔒 网络安全场景
```python
# 适用于入侵检测
cybersecurity_config = {
    'teacher_config': {
        'learning_rate': 0.015,
        'patience_threshold': 3,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.8,
            'adversarial_to_mastery': 0.9
        }
    },
    'student_config': {
        'initial_threshold': 0.4,  # 更敏感的检测
        'feature_dim': 20,         # 网络特征维度
        'exploration_rate': 0.05   # 较低探索率
    },
    'adversary_config': {
        'anomaly_types': ['stealth', 'aggressive', 'polymorphic'],
        'intensity_range': (0.05, 0.95),
        'adaptation_speed': 0.2    # 快速适应
    }
}
```

### 💰 金融风控场景
```python
# 适用于欺诈检测
financial_config = {
    'teacher_config': {
        'learning_rate': 0.008,    # 保守学习
        'difficulty_adjustment_rate': 0.05,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.85,
            'adversarial_to_mastery': 0.92
        }
    },
    'student_config': {
        'initial_threshold': 0.3,  # 高敏感度
        'feature_dim': 25,         # 丰富的金融特征
        'adaptation_rate': 0.08,   # 谨慎适应
        'memory_size': 500         # 长期记忆
    },
    'adversary_config': {
        'anomaly_types': ['sophisticated', 'social_engineering', 'insider'],
        'intensity_range': (0.02, 0.8),
        'creativity_factor': 0.15  # 适度创新
    }
}
```

## 📊 性能监控

### 实时监控代码
```python
import matplotlib.pyplot as plt
from collections import defaultdict

class RealTimeMonitor:
    def __init__(self):
        self.metrics = defaultdict(list)

    def update(self, results):
        """更新监控指标"""
        for result in results:
            for key, value in result.performance_metrics.items():
                self.metrics[key].append(value)

    def plot_live(self):
        """实时绘图"""
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 3, 1)
        plt.plot(self.metrics['student_performance'], label='Student')
        plt.plot(self.metrics['adversary_performance'], label='Adversary')
        plt.title('Performance Comparison')
        plt.legend()

        plt.subplot(1, 3, 2)
        plt.plot(self.metrics['teacher_effectiveness'])
        plt.title('Teacher Effectiveness')

        plt.subplot(1, 3, 3)
        plt.plot(self.metrics['overall_quality'])
        plt.title('Overall Quality')

        plt.tight_layout()
        plt.pause(0.1)

# 使用示例
monitor = RealTimeMonitor()

# 在训练循环中
for episode in range(config.max_episodes):
    episode_results = trainer._train_episode()
    monitor.update(episode_results)

    if episode % 10 == 0:
        monitor.plot_live()
```

## 🔧 常见问题解决

### ❓ 问题1：训练速度慢
```python
# 解决方案：减少轮次，增加批处理
config = TrainingConfig(
    max_episodes=50,           # 减少回合数
    max_rounds_per_episode=15, # 减少轮次
    convergence_threshold=0.02 # 放宽收敛条件
)
```

### ❓ 问题2：学生表现不佳
```python
# 解决方案：调整学习参数
student_config = {
    'initial_threshold': 0.4,    # 降低初始阈值
    'adaptation_rate': 0.15,     # 提高适应率
    'exploration_rate': 0.2      # 增加探索
}
```

### ❓ 问题3：对手过于强势
```python
# 解决方案：限制对手能力
adversary_config = {
    'intensity_range': (0.1, 0.7),  # 降低最大强度
    'creativity_factor': 0.1,       # 减少创新性
    'adaptation_speed': 0.1         # 降低适应速度
}
```

## 🎨 可视化结果

### 生成训练报告
```python
def generate_training_report(results):
    """生成可视化训练报告"""

    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))

    # 性能趋势
    axes[0, 0].plot(results['student_history'], label='Student')
    axes[0, 0].plot(results['adversary_history'], label='Adversary')
    axes[0, 0].set_title('Performance Trends')
    axes[0, 0].legend()

    # 博弈平衡
    axes[0, 1].plot(results['balance_history'])
    axes[0, 1].set_title('Game Balance')
    axes[0, 1].axhline(y=0.5, color='r', linestyle='--', alpha=0.5)

    # 收敛过程
    axes[0, 2].plot(results['convergence_metrics'])
    axes[0, 2].set_title('Convergence Process')

    # 策略分布
    strategy_counts = results['strategy_distribution']
    axes[1, 0].pie(strategy_counts.values(), labels=strategy_counts.keys())
    axes[1, 0].set_title('Strategy Distribution')

    # 阶段转换
    stage_timeline = results['stage_timeline']
    axes[1, 1].plot(stage_timeline)
    axes[1, 1].set_title('Curriculum Stages')

    # 最终统计
    final_stats = results['final_statistics']
    axes[1, 2].bar(final_stats.keys(), final_stats.values())
    axes[1, 2].set_title('Final Statistics')

    plt.tight_layout()
    plt.savefig('training_report.png', dpi=300, bbox_inches='tight')
    plt.show()

# 使用示例
generate_training_report(results)
```

## 🎓 进阶使用

### 自定义智能体
```python
class MyCustomStudent(StudentAgent):
    def select_strategy(self, game_state):
        # 添加自定义逻辑
        strategy = super().select_strategy(game_state)

        # 根据历史表现调整
        if len(self.performance_history) > 10:
            recent_perf = np.mean(self.performance_history[-10:])
            if recent_perf < 0.5:
                strategy['detection_threshold'] *= 0.9  # 更敏感

        return strategy
```

### 集成外部数据
```python
def load_real_data(dataset_path):
    """加载真实数据集"""
    # 加载你的数据
    data = torch.load(dataset_path)

    # 适配到框架
    questions = data['questions']
    responses = data['responses']

    return questions, responses

# 在训练中使用
questions, responses = load_real_data('your_dataset.pt')
trainer.set_real_data(questions, responses)
```

## 📚 下一步学习

1. 📖 **深入理论**: 阅读[完整文档](game_theoretic_framework_documentation.md)
2. 🔬 **高级配置**: 学习[配置指南](configuration_guide.md)
3. 🛠️ **自定义开发**: 参考[开发者指南](developer_guide.md)
4. 📊 **性能优化**: 查看[优化建议](performance_optimization.md)

## 💡 小贴士

- 🎯 **从小规模开始**: 先用少量回合测试配置
- 📊 **监控关键指标**: 重点关注博弈平衡和收敛性
- 🔄 **迭代优化**: 根据结果逐步调整参数
- 💾 **保存检查点**: 定期保存训练状态
- 📝 **记录实验**: 详细记录不同配置的效果

---

🎉 **恭喜！** 您已经掌握了博弈论异常检测框架的基础使用。开始您的智能异常检测之旅吧！
