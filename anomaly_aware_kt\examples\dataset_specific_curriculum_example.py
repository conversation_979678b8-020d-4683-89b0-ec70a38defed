#!/usr/bin/env python3
"""
数据集特定课程策略示例

演示教师智能体如何根据不同数据集特性设计针对性的课程策略。
这是博弈论异常检测框架中的核心创新功能。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from typing import Dict, List, Any
from dataclasses import dataclass

from anomaly_kt.game_theory.game_agents import TeacherAgent, GameState


@dataclass
class DatasetProfile:
    """数据集画像"""
    dataset_type: str
    domain_complexity: float
    feature_dimensionality: str
    temporal_dependency: bool
    sparsity_level: float
    noise_level: float
    class_imbalance: float
    adversarial_vulnerability: float
    # 领域特定属性
    threat_level: str = 'medium'
    regulatory_strictness: str = 'medium'
    clinical_setting: str = 'general'


def create_sample_game_state(stage: str = 'foundation') -> GameState:
    """创建示例博弈状态"""
    return GameState(
        round_number=10,
        student_performance={
            'detection_accuracy': 0.65,
            'false_positive_rate': 0.15,
            'learning_speed': 0.7,
            'stability': 0.6
        },
        adversary_success_rate=0.4,
        curriculum_stage=stage,
        difficulty_level=0.5,
        historical_outcomes=[
            {'student_success': True, 'adversary_success': False},
            {'student_success': False, 'adversary_success': True},
            {'student_success': True, 'adversary_success': False},
        ]
    )


def demonstrate_knowledge_tracing_curriculum():
    """演示知识追踪领域的课程策略"""
    print("🎓 知识追踪领域课程策略演示")
    print("=" * 50)
    
    # 创建知识追踪数据集画像
    kt_profile = DatasetProfile(
        dataset_type='knowledge_tracing',
        domain_complexity=0.6,
        feature_dimensionality='medium',
        temporal_dependency=True,
        sparsity_level=0.3,
        noise_level=0.1,
        class_imbalance=0.2,
        adversarial_vulnerability=0.5
    )
    
    # 创建教师智能体
    teacher_config = {
        'learning_rate': 0.01,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.75,
            'adversarial_to_mastery': 0.85
        }
    }
    teacher = TeacherAgent("kt_teacher", teacher_config)
    
    # 设计课程策略
    game_state = create_sample_game_state('foundation')
    curriculum = teacher.design_dataset_specific_curriculum(kt_profile.__dict__, game_state)
    
    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")
    
    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")
    
    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")
    
    print("\n🔧 特殊考虑:")
    for consideration, enabled in curriculum['special_considerations'].items():
        print(f"   • {consideration}: {'✅' if enabled else '❌'}")


def demonstrate_cybersecurity_curriculum():
    """演示网络安全领域的课程策略"""
    print("\n🔒 网络安全领域课程策略演示")
    print("=" * 50)
    
    # 创建网络安全数据集画像
    cyber_profile = DatasetProfile(
        dataset_type='cybersecurity',
        domain_complexity=0.8,
        feature_dimensionality='high',
        temporal_dependency=True,
        sparsity_level=0.2,
        noise_level=0.15,
        class_imbalance=0.1,
        adversarial_vulnerability=0.8,
        threat_level='high'  # 高威胁级别
    )
    
    teacher_config = {
        'learning_rate': 0.015,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.8,
            'adversarial_to_mastery': 0.9
        }
    }
    teacher = TeacherAgent("cyber_teacher", teacher_config)
    
    game_state = create_sample_game_state('adversarial')
    curriculum = teacher.design_dataset_specific_curriculum(cyber_profile.__dict__, game_state)
    
    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")
    
    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")
    
    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")


def demonstrate_financial_curriculum():
    """演示金融风控领域的课程策略"""
    print("\n💰 金融风控领域课程策略演示")
    print("=" * 50)
    
    # 创建金融数据集画像
    finance_profile = DatasetProfile(
        dataset_type='financial',
        domain_complexity=0.7,
        feature_dimensionality='high',
        temporal_dependency=True,
        sparsity_level=0.4,
        noise_level=0.05,  # 金融数据噪声较低
        class_imbalance=0.05,  # 欺诈案例稀少
        adversarial_vulnerability=0.6,
        regulatory_strictness='high'  # 严格监管
    )
    
    teacher_config = {
        'learning_rate': 0.008,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.85,
            'adversarial_to_mastery': 0.92
        }
    }
    teacher = TeacherAgent("finance_teacher", teacher_config)
    
    game_state = create_sample_game_state('mastery')
    curriculum = teacher.design_dataset_specific_curriculum(finance_profile.__dict__, game_state)
    
    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")
    
    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")
    
    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")


def demonstrate_healthcare_curriculum():
    """演示医疗健康领域的课程策略"""
    print("\n🏥 医疗健康领域课程策略演示")
    print("=" * 50)
    
    # 创建医疗数据集画像
    health_profile = DatasetProfile(
        dataset_type='healthcare',
        domain_complexity=0.9,  # 医疗领域复杂度最高
        feature_dimensionality='very_high',
        temporal_dependency=True,
        sparsity_level=0.6,
        noise_level=0.2,
        class_imbalance=0.1,
        adversarial_vulnerability=0.3,  # 医疗数据相对安全
        clinical_setting='icu'  # ICU环境
    )
    
    teacher_config = {
        'learning_rate': 0.005,  # 更保守的学习率
        'stage_thresholds': {
            'foundation_to_adversarial': 0.9,
            'adversarial_to_mastery': 0.95
        }
    }
    teacher = TeacherAgent("health_teacher", teacher_config)
    
    game_state = create_sample_game_state('foundation')
    curriculum = teacher.design_dataset_specific_curriculum(health_profile.__dict__, game_state)
    
    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")
    
    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")
    
    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")


def demonstrate_adaptive_curriculum_adjustment():
    """演示自适应课程调整"""
    print("\n🔄 自适应课程调整演示")
    print("=" * 50)
    
    # 创建教师智能体
    teacher_config = {'learning_rate': 0.01}
    teacher = TeacherAgent("adaptive_teacher", teacher_config)
    
    # 创建基础课程
    base_profile = DatasetProfile(
        dataset_type='general',
        domain_complexity=0.5,
        feature_dimensionality='medium',
        temporal_dependency=False,
        sparsity_level=0.3,
        noise_level=0.1,
        class_imbalance=0.15,
        adversarial_vulnerability=0.5
    )
    
    game_state = create_sample_game_state()
    base_curriculum = teacher.design_dataset_specific_curriculum(base_profile.__dict__, game_state)
    
    # 模拟性能历史 - 性能下降场景
    declining_performance = [
        {'detection_accuracy': 0.8, 'learning_speed': 0.6},
        {'detection_accuracy': 0.75, 'learning_speed': 0.5},
        {'detection_accuracy': 0.7, 'learning_speed': 0.4},
        {'detection_accuracy': 0.65, 'learning_speed': 0.3},
        {'detection_accuracy': 0.6, 'learning_speed': 0.2}
    ]
    
    print("📉 检测到性能下降，应用补救性调整...")
    adapted_curriculum = teacher.adapt_curriculum_to_performance(base_curriculum, declining_performance)
    
    # 比较调整前后的阈值
    original_threshold = base_curriculum['stage_design']['foundation']['mastery_threshold']
    adapted_threshold = adapted_curriculum['stage_design']['foundation']['mastery_threshold']
    
    print(f"原始掌握阈值: {original_threshold:.2f}")
    print(f"调整后阈值: {adapted_threshold:.2f}")
    print(f"调整幅度: {((adapted_threshold - original_threshold) / original_threshold * 100):+.1f}%")
    
    # 模拟快速进步场景
    print("\n📈 检测到快速进步，应用加速调整...")
    rapid_progress = [
        {'detection_accuracy': 0.6, 'learning_speed': 0.8},
        {'detection_accuracy': 0.7, 'learning_speed': 0.9},
        {'detection_accuracy': 0.8, 'learning_speed': 0.9},
        {'detection_accuracy': 0.85, 'learning_speed': 0.85},
        {'detection_accuracy': 0.9, 'learning_speed': 0.8}
    ]
    
    accelerated_curriculum = teacher.adapt_curriculum_to_performance(base_curriculum, rapid_progress)
    accelerated_threshold = accelerated_curriculum['stage_design']['foundation']['mastery_threshold']
    
    print(f"原始掌握阈值: {original_threshold:.2f}")
    print(f"加速后阈值: {accelerated_threshold:.2f}")
    print(f"调整幅度: {((accelerated_threshold - original_threshold) / original_threshold * 100):+.1f}%")


def main():
    """主函数"""
    print("🚀 数据集特定课程策略演示")
    print("=" * 60)
    print("展示教师智能体如何根据不同数据集特性设计针对性的课程策略")
    print("这是博弈论异常检测框架的核心创新功能之一")
    print("=" * 60)
    
    # 演示各领域的课程策略
    demonstrate_knowledge_tracing_curriculum()
    demonstrate_cybersecurity_curriculum()
    demonstrate_financial_curriculum()
    demonstrate_healthcare_curriculum()
    
    # 演示自适应调整
    demonstrate_adaptive_curriculum_adjustment()
    
    print("\n🎉 演示完成！")
    print("💡 这展示了教师智能体如何根据数据集特性智能地设计课程策略")
    print("🔬 这是传统机器学习方法无法实现的高级功能")


if __name__ == "__main__":
    main()
