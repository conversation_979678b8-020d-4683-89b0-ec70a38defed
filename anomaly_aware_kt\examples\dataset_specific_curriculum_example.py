#!/usr/bin/env python3
"""
数据集特定课程策略示例

演示教师智能体如何根据不同数据集特性设计针对性的课程策略。
这是博弈论异常检测框架中的核心创新功能。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from typing import Dict, List, Any
from dataclasses import dataclass

from game_theory.game_agents import TeacherAgent, GameState


@dataclass
class KnowledgeTracingDatasetProfile:
    """知识追踪数据集画像"""
    dataset_name: str
    n_questions: int
    n_pid: int
    domain_focus: str
    complexity: str
    scale: str
    # 知识追踪特定属性
    interaction_richness: str = 'moderate'
    temporal_granularity: str = 'coarse'
    skill_modeling_depth: str = 'basic'
    hint_availability: bool = False
    avg_sequence_length: float = 100.0


def create_sample_game_state(stage: str = 'foundation') -> GameState:
    """创建示例博弈状态"""
    return GameState(
        round_number=10,
        student_performance={
            'detection_accuracy': 0.65,
            'false_positive_rate': 0.15,
            'learning_speed': 0.7,
            'stability': 0.6
        },
        adversary_success_rate=0.4,
        curriculum_stage=stage,
        difficulty_level=0.5,
        historical_outcomes=[
            {'student_success': True, 'adversary_success': False},
            {'student_success': False, 'adversary_success': True},
            {'student_success': True, 'adversary_success': False},
        ]
    )


def demonstrate_assist09_curriculum():
    """演示ASSIST09数据集的课程策略"""
    print("🎓 ASSIST09数据集课程策略演示")
    print("=" * 50)

    # 创建ASSIST09数据集画像
    assist09_profile = KnowledgeTracingDatasetProfile(
        dataset_name='assist09',
        n_questions=123,
        n_pid=17751,
        domain_focus='mathematics_problem_solving',
        complexity='moderate',
        scale='medium',
        interaction_richness='moderate',
        temporal_granularity='coarse',
        skill_modeling_depth='basic',
        hint_availability=True,
        avg_sequence_length=200.0
    )

    # 创建教师智能体
    teacher_config = {
        'learning_rate': 0.01,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.75,
            'adversarial_to_mastery': 0.85
        }
    }
    teacher = TeacherAgent("kt_teacher", teacher_config)

    # 设计课程策略
    game_state = create_sample_game_state('foundation')
    curriculum = teacher.design_dataset_specific_curriculum(assist09_profile.__dict__, game_state)

    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 领域焦点: {curriculum['domain_focus']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")

    # 显示数据集特征
    dataset_chars = curriculum['dataset_characteristics']
    print(f"\n📊 数据集特征:")
    print(f"   • 规模: {dataset_chars['scale']}")
    print(f"   • 问题数: {dataset_chars['n_questions']}")
    print(f"   • 问题类型数: {dataset_chars['n_pid']}")
    print(f"   • 复杂度: {dataset_chars['complexity']}")
    print(f"   • 稀疏性: {dataset_chars['sparsity']}")

    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"\n🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")

    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")

    print("\n🔧 特殊焦点:")
    for focus, enabled in current_stage['special_focus'].items():
        print(f"   • {focus}: {'✅' if enabled else '❌'}")


def demonstrate_assist17_curriculum():
    """演示ASSIST17数据集的课程策略"""
    print("\n🔬 ASSIST17数据集课程策略演示")
    print("=" * 50)

    # 创建ASSIST17数据集画像
    assist17_profile = KnowledgeTracingDatasetProfile(
        dataset_name='assist17',
        n_questions=102,
        n_pid=3162,
        domain_focus='mathematics_problem_solving',
        complexity='high',
        scale='large',
        interaction_richness='very_high',
        temporal_granularity='fine',
        skill_modeling_depth='advanced',
        hint_availability=True,
        avg_sequence_length=551.9
    )

    teacher_config = {
        'learning_rate': 0.008,  # 更精细的学习率
        'stage_thresholds': {
            'foundation_to_adversarial': 0.75,
            'adversarial_to_mastery': 0.82
        }
    }
    teacher = TeacherAgent("assist17_teacher", teacher_config)

    game_state = create_sample_game_state('adversarial')
    curriculum = teacher.design_dataset_specific_curriculum(assist17_profile.__dict__, game_state)

    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 领域焦点: {curriculum['domain_focus']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")

    # 显示数据集特征
    dataset_chars = curriculum['dataset_characteristics']
    print(f"\n📊 数据集特征:")
    print(f"   • 规模: {dataset_chars['scale']}")
    print(f"   • 问题数: {dataset_chars['n_questions']}")
    print(f"   • 问题类型数: {dataset_chars['n_pid']}")
    print(f"   • 复杂度: {dataset_chars['complexity']}")
    print(f"   • 交互丰富度: {dataset_chars['interaction_richness']}")
    print(f"   • 时序粒度: {dataset_chars['temporal_granularity']}")

    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"\n🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")

    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")

    print("\n🔧 特殊焦点:")
    for focus, enabled in current_stage['special_focus'].items():
        print(f"   • {focus}: {'✅' if enabled else '❌'}")

    # 显示ASSIST17特有优化
    if 'assist17_optimizations' in curriculum:
        print("\n🚀 ASSIST17特有优化:")
        for opt, enabled in curriculum['assist17_optimizations'].items():
            print(f"   • {opt}: {'✅' if enabled else '❌'}")


def demonstrate_statics_curriculum():
    """演示Statics数据集的课程策略"""
    print("\n⚖️ Statics数据集课程策略演示")
    print("=" * 50)

    # 创建Statics数据集画像
    statics_profile = KnowledgeTracingDatasetProfile(
        dataset_name='statics',
        n_questions=1223,
        n_pid=1223,  # 假设每个问题都是唯一的
        domain_focus='physics_statics',
        complexity='high',
        scale='large',
        interaction_richness='moderate',
        temporal_granularity='coarse',
        skill_modeling_depth='deep',
        hint_availability=False,
        avg_sequence_length=150.0
    )

    teacher_config = {
        'learning_rate': 0.005,  # 物理概念需要更慢的学习
        'stage_thresholds': {
            'foundation_to_adversarial': 0.72,
            'adversarial_to_mastery': 0.9
        }
    }
    teacher = TeacherAgent("statics_teacher", teacher_config)

    game_state = create_sample_game_state('foundation')
    curriculum = teacher.design_dataset_specific_curriculum(statics_profile.__dict__, game_state)

    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 领域焦点: {curriculum['domain_focus']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")

    # 显示数据集特征
    dataset_chars = curriculum['dataset_characteristics']
    print(f"\n📊 数据集特征:")
    print(f"   • 规模: {dataset_chars['scale']}")
    print(f"   • 问题数: {dataset_chars['n_questions']}")
    print(f"   • 复杂度: {dataset_chars['complexity']}")
    print(f"   • 领域特异性: {dataset_chars['domain_specificity']}")
    print(f"   • 概念深度: {dataset_chars['conceptual_depth']}")

    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"\n🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")

    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")

    print("\n🔧 特殊焦点:")
    for focus, enabled in current_stage['special_focus'].items():
        print(f"   • {focus}: {'✅' if enabled else '❌'}")


def demonstrate_algebra05_curriculum():
    """演示Algebra05数据集的课程策略"""
    print("\n🔢 Algebra05数据集课程策略演示")
    print("=" * 50)

    # 创建Algebra05数据集画像
    algebra05_profile = KnowledgeTracingDatasetProfile(
        dataset_name='algebra05',
        n_questions=138,
        n_pid=1084,
        domain_focus='algebra_mathematics',
        complexity='moderate_to_high',
        scale='medium',
        interaction_richness='moderate',
        temporal_granularity='coarse',
        skill_modeling_depth='moderate',
        hint_availability=True,
        avg_sequence_length=120.0
    )

    teacher_config = {
        'learning_rate': 0.01,
        'stage_thresholds': {
            'foundation_to_adversarial': 0.73,
            'adversarial_to_mastery': 0.87
        }
    }
    teacher = TeacherAgent("algebra05_teacher", teacher_config)

    game_state = create_sample_game_state('adversarial')
    curriculum = teacher.design_dataset_specific_curriculum(algebra05_profile.__dict__, game_state)

    print(f"📚 课程哲学: {curriculum['curriculum_philosophy']}")
    print(f"🎯 领域焦点: {curriculum['domain_focus']}")
    print(f"🎯 当前阶段: {game_state.curriculum_stage}")

    # 显示数据集特征
    dataset_chars = curriculum['dataset_characteristics']
    print(f"\n📊 数据集特征:")
    print(f"   • 规模: {dataset_chars['scale']}")
    print(f"   • 问题数: {dataset_chars['n_questions']}")
    print(f"   • 问题类型数: {dataset_chars['n_pid']}")
    print(f"   • 复杂度: {dataset_chars['complexity']}")
    print(f"   • 程序性重点: {dataset_chars['procedural_emphasis']}")

    current_stage = curriculum['stage_design'][game_state.curriculum_stage]
    print(f"\n🔍 阶段焦点: {current_stage['focus']}")
    print(f"🎭 异常类型: {current_stage['anomaly_types']}")
    print(f"📈 难度递增: {current_stage['difficulty_progression']}")
    print(f"🎯 掌握阈值: {current_stage['mastery_threshold']:.2f}")

    print("\n🚨 干预触发器:")
    for trigger, threshold in current_stage['intervention_triggers'].items():
        print(f"   • {trigger}: {threshold}")

    print("\n🔧 特殊焦点:")
    for focus, enabled in current_stage['special_focus'].items():
        print(f"   • {focus}: {'✅' if enabled else '❌'}")

    print("\n🔧 特殊考虑:")
    for consideration, enabled in curriculum['special_considerations'].items():
        print(f"   • {consideration}: {'✅' if enabled else '❌'}")





def main():
    """主函数"""
    print("🚀 知识追踪领域数据集特定课程策略演示")
    print("=" * 60)
    print("展示教师智能体如何针对知识追踪领域的不同数据集设计特定课程策略")
    print("专注于ASSIST、Statics、Algebra等知识追踪数据集")
    print("=" * 60)

    # 演示知识追踪领域不同数据集的课程策略
    demonstrate_assist09_curriculum()
    demonstrate_assist17_curriculum()
    demonstrate_statics_curriculum()
    demonstrate_algebra05_curriculum()

    print("\n🎉 演示完成！")
    print("💡 这展示了教师智能体如何针对知识追踪领域的不同数据集智能地设计课程策略")
    print("🔬 每个数据集都有其独特的特征和挑战，需要专门的课程策略")
    print("📊 主要数据集特点:")
    print("   • ASSIST09: 中等规模，高稀疏性，基础数学技能")
    print("   • ASSIST17: 大规模，丰富交互，精细时序建模")
    print("   • Statics: 大规模，高复杂度，深度物理概念")
    print("   • Algebra05: 中等规模，程序性技能，代数推理")
    print("🎯 这种数据集感知的课程设计是博弈论框架的核心创新！")


if __name__ == "__main__":
    main()
