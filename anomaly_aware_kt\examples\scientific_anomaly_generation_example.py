"""
科学异常生成器完整使用示例

展示如何使用基于认知科学理论和IRT难度建模的异常生成框架。
"""

import os
import sys
import yaml
import torch
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from anomaly_generation import (
    ScientificAnomalyGenerator,
    IRTDifficultyModel,
    QualityController,
    CognitiveLoadAnomaly
)
from anomaly_generation.generators.base_strategy import GenerationContext
from anomaly_generation.taxonomy.base_anomaly import AnomalySeverity


def load_config(config_path: str = "anomaly_generation/config/scientific_anomaly_config.yaml"):
    """加载配置文件"""
    config_file = project_root / config_path

    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    return config


def create_sample_data(batch_size: int = 8, seq_len: int = 50):
    """创建示例数据"""
    # 模拟学习序列数据
    questions = torch.randint(0, 20, (batch_size, seq_len))  # 20个不同问题
    responses = torch.randint(0, 2, (batch_size, seq_len))   # 0/1响应

    # 模拟问题类型ID
    problem_ids = torch.randint(0, 100, (batch_size, seq_len))

    # 模拟答题时间（秒）
    answer_times = torch.randint(5, 120, (batch_size, seq_len))

    # 模拟知识状态
    knowledge_states = torch.rand(batch_size, seq_len)

    return {
        'questions': questions,
        'responses': responses,
        'problem_ids': problem_ids,
        'answer_times': answer_times,
        'knowledge_states': knowledge_states
    }


def demonstrate_irt_modeling():
    """演示IRT难度建模"""
    print("🔬 IRT难度建模演示")
    print("=" * 50)

    # 创建IRT模型
    irt_model = IRTDifficultyModel(model_type="2PL", device='cpu')

    # 创建示例响应数据
    batch_size, n_items = 100, 20
    responses = torch.randint(0, 2, (batch_size, n_items))

    print(f"📊 校准IRT模型 - 数据规模: {batch_size}x{n_items}")

    # 校准模型
    parameters = irt_model.calibrate_items(responses)

    print(f"✅ 校准完成")
    print(f"   难度参数范围: {parameters.difficulty.min():.3f} - {parameters.difficulty.max():.3f}")
    if parameters.discrimination is not None:
        print(f"   区分度参数范围: {parameters.discrimination.min():.3f} - {parameters.discrimination.max():.3f}")

    # 估计能力参数
    sample_responses = responses[:5]  # 取前5个学生
    abilities = irt_model.estimate_ability(sample_responses, parameters)
    print(f"   样本能力参数: {abilities.tolist()}")

    return irt_model


def demonstrate_cognitive_load_anomaly():
    """演示认知负荷异常生成"""
    print("\n🧠 认知负荷异常生成演示")
    print("=" * 50)

    # 配置认知负荷异常
    config = {
        'intrinsic_weight': 0.6,
        'extraneous_weight': 0.3,
        'germane_weight': 0.1,
        'overload_threshold': 0.8,
        'fatigue_onset': 0.6
    }

    # 创建认知负荷异常生成器
    cognitive_anomaly = CognitiveLoadAnomaly(config)

    # 创建示例数据
    seq_len = 30
    sequence = torch.randint(0, 2, (seq_len,))
    difficulties = torch.rand(seq_len)
    knowledge_states = torch.cumsum(torch.rand(seq_len) * 0.1, dim=0)

    # 创建异常上下文
    from anomaly_generation.taxonomy.base_anomaly import AnomalyContext
    context = AnomalyContext(
        sequence_length=seq_len,
        difficulty_profile=difficulties,
        knowledge_state=knowledge_states,
        temporal_position=0.7
    )

    print(f"📝 原始序列: {sequence.tolist()}")
    print(f"📊 难度分布: 均值={difficulties.mean():.3f}, 标准差={difficulties.std():.3f}")

    # 生成异常
    anomaly_sequence, anomaly_mask = cognitive_anomaly.generate_anomaly(
        sequence, context, AnomalySeverity.MEDIUM
    )

    print(f"🎯 异常序列: {anomaly_sequence.tolist()}")
    print(f"🔍 异常掩码: {anomaly_mask.tolist()}")
    print(f"📈 异常比例: {anomaly_mask.float().mean():.2%}")

    # 验证异常质量
    quality_metrics = cognitive_anomaly.validate_anomaly(
        sequence, anomaly_sequence, anomaly_mask, context
    )

    print(f"✅ 质量指标:")
    for metric, value in quality_metrics.items():
        print(f"   {metric}: {value:.3f}")

    return cognitive_anomaly


def demonstrate_scientific_generator():
    """演示科学异常生成器"""
    print("\n🔬 科学异常生成器演示")
    print("=" * 50)

    # 加载配置
    config = load_config()

    # 创建IRT模型
    irt_model = IRTDifficultyModel(model_type="2PL", device='cpu')

    # 创建质量控制器
    quality_controller = QualityController(config['quality_control'])

    # 创建科学异常生成器
    generator = ScientificAnomalyGenerator(
        config=config['generation'],
        irt_model=irt_model,
        quality_controller=quality_controller,
        random_seed=42
    )

    # 注册认知负荷策略（简化演示）
    cognitive_strategy = CognitiveLoadAnomaly(config['strategies']['cognitive_load'])
    # generator.register_strategy(cognitive_strategy)  # 需要实现策略适配器

    # 创建示例数据
    data = create_sample_data(batch_size=1, seq_len=40)

    # 提取单个序列
    questions = data['questions'][0]
    responses = data['responses'][0]

    print(f"📊 输入数据:")
    print(f"   序列长度: {len(responses)}")
    print(f"   问题范围: {questions.min().item()} - {questions.max().item()}")
    print(f"   正确率: {responses.float().mean():.2%}")

    # 生成异常
    try:
        result = generator.generate_anomalies(
            questions=questions,
            responses=responses,
            anomaly_ratio=0.15,
            severity=AnomalySeverity.MEDIUM,
            knowledge_states=data['knowledge_states'][0],
            answer_times=data['answer_times'][0]
        )

        print(f"\n🎯 生成结果:")
        print(f"   异常数量: {result.anomaly_info['total_anomalies']}")
        print(f"   实际比例: {result.anomaly_info['actual_ratio']:.2%}")
        print(f"   使用策略: {result.anomaly_info['strategies_used']}")
        print(f"   整体质量: {result.quality_metrics.get('overall_quality', 0):.3f}")

        # 显示异常位置
        anomaly_positions = result.anomaly_info['anomaly_positions']
        print(f"   异常位置: {anomaly_positions[:10]}{'...' if len(anomaly_positions) > 10 else ''}")

    except Exception as e:
        print(f"❌ 生成失败: {e}")
        print("   这是因为策略注册需要完整的实现")

    return generator


def demonstrate_quality_control():
    """演示质量控制"""
    print("\n✅ 质量控制演示")
    print("=" * 50)

    # 创建质量控制器
    config = load_config()
    quality_controller = QualityController(config['quality_control'])

    # 创建示例数据
    seq_len = 25
    original = torch.randint(0, 2, (seq_len,))
    anomaly = original.clone()

    # 手动创建一些异常
    anomaly_positions = [5, 8, 12, 18, 22]
    mask = torch.zeros_like(original, dtype=torch.bool)

    for pos in anomaly_positions:
        anomaly[pos] = 1 - anomaly[pos]  # 翻转
        mask[pos] = True

    # 创建上下文
    from anomaly_generation.taxonomy.base_anomaly import AnomalyContext
    difficulties = torch.rand(seq_len)
    context = AnomalyContext(
        sequence_length=seq_len,
        difficulty_profile=difficulties,
        knowledge_state=torch.rand(seq_len),
        temporal_position=0.5
    )

    print(f"📊 质量验证数据:")
    print(f"   原始序列: {original.tolist()}")
    print(f"   异常序列: {anomaly.tolist()}")
    print(f"   异常位置: {anomaly_positions}")

    # 执行质量验证
    quality_metrics = quality_controller.validate_generation(
        original, anomaly, mask, context
    )

    print(f"\n📈 质量指标:")
    for metric, value in quality_metrics.items():
        print(f"   {metric}: {value:.3f}")

    # 判断质量是否合格
    overall_quality = quality_metrics['overall_quality']
    threshold = config['quality_control']['thresholds']['overall_quality']

    if overall_quality >= threshold:
        print(f"✅ 质量合格 ({overall_quality:.3f} >= {threshold})")
    else:
        print(f"❌ 质量不合格 ({overall_quality:.3f} < {threshold})")


def main():
    """主函数"""
    print("🎓 科学异常生成框架完整演示")
    print("=" * 60)

    try:
        # 1. IRT难度建模演示
        irt_model = demonstrate_irt_modeling()

        # 2. 认知负荷异常演示
        cognitive_anomaly = demonstrate_cognitive_load_anomaly()

        # 3. 科学异常生成器演示
        generator = demonstrate_scientific_generator()

        # 4. 质量控制演示
        demonstrate_quality_control()

        print("\n🎉 演示完成！")
        print("\n📝 使用建议:")
        print("   1. 根据具体数据集调整配置参数")
        print("   2. 实现完整的策略注册机制")
        print("   3. 集成到实际的训练流程中")
        print("   4. 定期验证生成质量")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
