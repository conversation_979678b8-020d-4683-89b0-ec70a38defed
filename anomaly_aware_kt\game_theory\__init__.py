"""
博弈论异常检测训练框架

基于教师-学生-对手三方博弈模型的异常检测训练框架，结合：
1. 不完全信息博弈理论
2. 自适应课程学习
3. 多智能体强化学习
4. 对抗训练机制
"""

from .game_agents import TeacherAgent, StudentAgent, AdversaryAgent, GameState
from .game_environment import GameEnvironment
from .game_trainer import GameTheoreticTrainer, TrainingConfig

__version__ = "1.0.0"
__author__ = "Game-Theoretic Anomaly Detection Team"

__all__ = [
    # 核心智能体
    "TeacherAgent",
    "StudentAgent",
    "AdversaryAgent",
    "GameState",

    # 博弈环境
    "GameEnvironment",

    # 训练器
    "GameTheoreticTrainer",
    "TrainingConfig",
]
