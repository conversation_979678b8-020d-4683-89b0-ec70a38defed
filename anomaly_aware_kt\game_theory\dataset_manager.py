"""
数据集管理器 - 博弈论框架中的数据集角色实现

数据集在博弈论框架中扮演多重角色：
1. 博弈环境的基础设施
2. 教师的知识来源和课程材料
3. 学生-对手博弈的战场
4. 动态难度调节的依据
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import random
from collections import defaultdict

from .game_agents import GameState


class DatasetRole(Enum):
    """数据集角色枚举"""
    ENVIRONMENT = "environment"      # 环境基础
    KNOWLEDGE_SOURCE = "knowledge"   # 知识来源
    BATTLEGROUND = "battleground"    # 博弈战场
    CURRICULUM_MATERIAL = "curriculum" # 课程材料


@dataclass
class DataSample:
    """数据样本"""
    features: torch.Tensor           # 特征向量
    label: int                       # 标签 (0: 正常, 1: 异常)
    difficulty: float                # 难度评分
    domain: str                      # 领域标识
    metadata: Dict[str, Any]         # 元数据
    
    def to_game_context(self) -> Dict[str, Any]:
        """转换为博弈上下文"""
        return {
            'features': self.features,
            'true_label': self.label,
            'difficulty': self.difficulty,
            'domain': self.domain,
            'sample_id': self.metadata.get('id', -1)
        }


@dataclass
class DatasetPartition:
    """数据集分区"""
    samples: List[DataSample]
    difficulty_range: Tuple[float, float]
    anomaly_ratio: float
    stage: str                       # foundation, adversarial, mastery
    
    def get_samples_by_difficulty(self, min_diff: float, max_diff: float) -> List[DataSample]:
        """按难度获取样本"""
        return [s for s in self.samples if min_diff <= s.difficulty <= max_diff]


class GameDatasetManager:
    """
    博弈数据集管理器
    
    管理数据集在博弈论框架中的多重角色，包括：
    - 环境状态管理
    - 课程材料组织
    - 博弈战场构建
    - 动态难度调节
    """
    
    def __init__(self, 
                 dataset_config: Dict[str, Any],
                 enable_dynamic_difficulty: bool = True):
        """
        初始化数据集管理器
        
        Args:
            dataset_config: 数据集配置
            enable_dynamic_difficulty: 是否启用动态难度
        """
        self.config = dataset_config
        self.enable_dynamic_difficulty = enable_dynamic_difficulty
        
        # 数据存储
        self.raw_dataset = None
        self.partitions = {}  # 按阶段分区的数据
        self.difficulty_cache = {}
        
        # 博弈状态
        self.current_samples = []
        self.used_samples = set()
        self.difficulty_history = []
        
        # 统计信息
        self.usage_stats = defaultdict(int)
        self.performance_stats = defaultdict(list)
        
    def load_dataset(self, dataset_path: str, dataset_type: str = "standard"):
        """
        加载数据集
        
        Args:
            dataset_path: 数据集路径
            dataset_type: 数据集类型 (standard, knowledge_tracing, cybersecurity, financial)
        """
        if dataset_type == "knowledge_tracing":
            self.raw_dataset = self._load_knowledge_tracing_dataset(dataset_path)
        elif dataset_type == "cybersecurity":
            self.raw_dataset = self._load_cybersecurity_dataset(dataset_path)
        elif dataset_type == "financial":
            self.raw_dataset = self._load_financial_dataset(dataset_path)
        else:
            self.raw_dataset = self._load_standard_dataset(dataset_path)
            
        # 计算难度并分区
        self._compute_sample_difficulties()
        self._create_curriculum_partitions()
        
        print(f"📊 数据集加载完成: {len(self.raw_dataset)} 样本")
        self._print_dataset_statistics()
        
    def _load_knowledge_tracing_dataset(self, dataset_path: str) -> List[DataSample]:
        """加载知识追踪数据集"""
        # 这里应该根据实际的知识追踪数据格式实现
        # 示例实现
        data = torch.load(dataset_path)
        
        samples = []
        for i, (features, label) in enumerate(zip(data['features'], data['labels'])):
            sample = DataSample(
                features=features,
                label=label,
                difficulty=0.5,  # 初始难度，后续计算
                domain="knowledge_tracing",
                metadata={'id': i, 'student_id': data.get('student_ids', [0])[i]}
            )
            samples.append(sample)
            
        return samples
        
    def _load_cybersecurity_dataset(self, dataset_path: str) -> List[DataSample]:
        """加载网络安全数据集"""
        # 示例实现
        data = torch.load(dataset_path)
        
        samples = []
        for i, (features, label) in enumerate(zip(data['features'], data['labels'])):
            sample = DataSample(
                features=features,
                label=label,
                difficulty=0.5,
                domain="cybersecurity",
                metadata={'id': i, 'attack_type': data.get('attack_types', ['unknown'])[i]}
            )
            samples.append(sample)
            
        return samples
        
    def _load_financial_dataset(self, dataset_path: str) -> List[DataSample]:
        """加载金融数据集"""
        # 示例实现
        data = torch.load(dataset_path)
        
        samples = []
        for i, (features, label) in enumerate(zip(data['features'], data['labels'])):
            sample = DataSample(
                features=features,
                label=label,
                difficulty=0.5,
                domain="financial",
                metadata={'id': i, 'transaction_type': data.get('transaction_types', ['normal'])[i]}
            )
            samples.append(sample)
            
        return samples
        
    def _load_standard_dataset(self, dataset_path: str) -> List[DataSample]:
        """加载标准数据集"""
        data = torch.load(dataset_path)
        
        samples = []
        for i, (features, label) in enumerate(zip(data['features'], data['labels'])):
            sample = DataSample(
                features=features,
                label=label,
                difficulty=0.5,
                domain="general",
                metadata={'id': i}
            )
            samples.append(sample)
            
        return samples
        
    def _compute_sample_difficulties(self):
        """计算样本难度"""
        print("🔍 计算样本难度...")
        
        for sample in self.raw_dataset:
            # 基于多种因素计算难度
            difficulty = self._calculate_intrinsic_difficulty(sample)
            sample.difficulty = difficulty
            self.difficulty_cache[sample.metadata['id']] = difficulty
            
    def _calculate_intrinsic_difficulty(self, sample: DataSample) -> float:
        """计算样本内在难度"""
        
        # 方法1: 基于特征复杂度
        feature_complexity = self._calculate_feature_complexity(sample.features)
        
        # 方法2: 基于标签稀有度
        label_rarity = self._calculate_label_rarity(sample.label)
        
        # 方法3: 基于领域知识
        domain_difficulty = self._calculate_domain_difficulty(sample)
        
        # 综合难度计算
        difficulty = (
            0.4 * feature_complexity +
            0.3 * label_rarity +
            0.3 * domain_difficulty
        )
        
        return np.clip(difficulty, 0.0, 1.0)
        
    def _calculate_feature_complexity(self, features: torch.Tensor) -> float:
        """计算特征复杂度"""
        # 基于特征的统计特性
        feature_std = torch.std(features).item()
        feature_entropy = self._calculate_entropy(features)
        
        complexity = 0.6 * feature_std + 0.4 * feature_entropy
        return min(1.0, complexity)
        
    def _calculate_entropy(self, features: torch.Tensor) -> float:
        """计算特征熵"""
        # 简化的熵计算
        hist = torch.histc(features, bins=10, min=features.min(), max=features.max())
        hist = hist / hist.sum()
        hist = hist[hist > 0]  # 避免log(0)
        
        entropy = -torch.sum(hist * torch.log(hist)).item()
        return entropy / np.log(10)  # 归一化
        
    def _calculate_label_rarity(self, label: int) -> float:
        """计算标签稀有度"""
        total_samples = len(self.raw_dataset)
        label_count = sum(1 for s in self.raw_dataset if s.label == label)
        
        rarity = 1.0 - (label_count / total_samples)
        return rarity
        
    def _calculate_domain_difficulty(self, sample: DataSample) -> float:
        """计算领域特定难度"""
        domain_difficulties = {
            "knowledge_tracing": 0.6,
            "cybersecurity": 0.8,
            "financial": 0.7,
            "general": 0.5
        }
        
        return domain_difficulties.get(sample.domain, 0.5)
        
    def _create_curriculum_partitions(self):
        """创建课程分区"""
        print("📚 创建课程分区...")
        
        # 按难度排序
        sorted_samples = sorted(self.raw_dataset, key=lambda x: x.difficulty)
        
        # 分成三个阶段
        n_samples = len(sorted_samples)
        foundation_end = int(n_samples * 0.4)
        adversarial_end = int(n_samples * 0.8)
        
        self.partitions = {
            'foundation': DatasetPartition(
                samples=sorted_samples[:foundation_end],
                difficulty_range=(0.0, 0.4),
                anomaly_ratio=self._calculate_anomaly_ratio(sorted_samples[:foundation_end]),
                stage='foundation'
            ),
            'adversarial': DatasetPartition(
                samples=sorted_samples[foundation_end:adversarial_end],
                difficulty_range=(0.4, 0.8),
                anomaly_ratio=self._calculate_anomaly_ratio(sorted_samples[foundation_end:adversarial_end]),
                stage='adversarial'
            ),
            'mastery': DatasetPartition(
                samples=sorted_samples[adversarial_end:],
                difficulty_range=(0.8, 1.0),
                anomaly_ratio=self._calculate_anomaly_ratio(sorted_samples[adversarial_end:]),
                stage='mastery'
            )
        }
        
    def _calculate_anomaly_ratio(self, samples: List[DataSample]) -> float:
        """计算异常比例"""
        if not samples:
            return 0.0
        anomaly_count = sum(1 for s in samples if s.label == 1)
        return anomaly_count / len(samples)
        
    def get_game_environment_state(self, game_state: GameState) -> Dict[str, Any]:
        """获取博弈环境状态"""
        
        # 根据当前博弈状态选择合适的数据
        stage = game_state.curriculum_stage
        difficulty_level = game_state.difficulty_level
        
        # 从对应分区获取样本
        partition = self.partitions.get(stage, self.partitions['foundation'])
        
        # 根据难度级别筛选样本
        min_difficulty = max(0.0, difficulty_level - 0.2)
        max_difficulty = min(1.0, difficulty_level + 0.2)
        
        candidate_samples = partition.get_samples_by_difficulty(min_difficulty, max_difficulty)
        
        # 避免重复使用
        unused_samples = [s for s in candidate_samples if s.metadata['id'] not in self.used_samples]
        
        if not unused_samples:
            # 如果没有未使用的样本，重置使用记录
            self.used_samples.clear()
            unused_samples = candidate_samples
            
        # 选择样本
        if unused_samples:
            selected_sample = random.choice(unused_samples)
            self.used_samples.add(selected_sample.metadata['id'])
            self.current_samples.append(selected_sample)
            
            return {
                'sample': selected_sample,
                'environment_state': {
                    'stage': stage,
                    'difficulty_level': difficulty_level,
                    'available_samples': len(unused_samples),
                    'partition_info': {
                        'total_samples': len(partition.samples),
                        'difficulty_range': partition.difficulty_range,
                        'anomaly_ratio': partition.anomaly_ratio
                    }
                }
            }
        else:
            return {'sample': None, 'environment_state': None}
            
    def provide_teacher_knowledge(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """为教师提供知识"""
        
        query_type = query.get('type', 'general')
        
        if query_type == 'difficulty_distribution':
            return self._get_difficulty_distribution()
        elif query_type == 'student_performance_prediction':
            return self._predict_student_performance(query)
        elif query_type == 'curriculum_recommendation':
            return self._recommend_curriculum_adjustment(query)
        else:
            return self._get_general_dataset_info()
            
    def _get_difficulty_distribution(self) -> Dict[str, Any]:
        """获取难度分布"""
        difficulties = [s.difficulty for s in self.raw_dataset]
        
        return {
            'mean_difficulty': np.mean(difficulties),
            'std_difficulty': np.std(difficulties),
            'difficulty_histogram': np.histogram(difficulties, bins=10)[0].tolist(),
            'stage_distributions': {
                stage: {
                    'mean': np.mean([s.difficulty for s in partition.samples]),
                    'count': len(partition.samples)
                }
                for stage, partition in self.partitions.items()
            }
        }
        
    def _predict_student_performance(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """预测学生表现"""
        student_history = query.get('student_history', [])
        target_difficulty = query.get('target_difficulty', 0.5)
        
        # 基于历史表现预测
        if student_history:
            recent_performance = np.mean(student_history[-10:])
            predicted_accuracy = max(0.1, min(0.9, recent_performance * (1.1 - target_difficulty)))
        else:
            predicted_accuracy = 0.5
            
        return {
            'predicted_accuracy': predicted_accuracy,
            'confidence': 0.7,
            'recommended_samples': self._recommend_samples_for_difficulty(target_difficulty)
        }
        
    def _recommend_curriculum_adjustment(self, query: Dict[str, Any]) -> Dict[str, Any]:
        """推荐课程调整"""
        current_stage = query.get('current_stage', 'foundation')
        student_performance = query.get('student_performance', 0.5)
        adversary_performance = query.get('adversary_performance', 0.5)
        
        # 基于表现推荐调整
        if student_performance > 0.8 and adversary_performance < 0.6:
            recommendation = "increase_difficulty"
            target_stage = self._get_next_stage(current_stage)
        elif student_performance < 0.4:
            recommendation = "decrease_difficulty"
            target_stage = self._get_previous_stage(current_stage)
        else:
            recommendation = "maintain"
            target_stage = current_stage
            
        return {
            'recommendation': recommendation,
            'target_stage': target_stage,
            'reasoning': self._generate_recommendation_reasoning(
                student_performance, adversary_performance
            )
        }
        
    def create_battleground(self, 
                          student_strategy: Dict[str, Any],
                          adversary_strategy: Dict[str, Any],
                          teacher_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """创建学生-对手博弈战场"""
        
        # 获取当前环境样本
        current_sample = self.current_samples[-1] if self.current_samples else None
        
        if current_sample is None:
            return {'error': 'No current sample available'}
            
        # 构建博弈上下文
        battleground = {
            'original_sample': current_sample,
            'game_context': current_sample.to_game_context(),
            'student_view': self._create_student_view(current_sample, student_strategy),
            'adversary_view': self._create_adversary_view(current_sample, adversary_strategy),
            'teacher_guidance': teacher_guidance,
            'battle_metadata': {
                'sample_id': current_sample.metadata['id'],
                'true_difficulty': current_sample.difficulty,
                'domain': current_sample.domain
            }
        }
        
        return battleground
        
    def _create_student_view(self, sample: DataSample, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """创建学生视角"""
        return {
            'features': sample.features,
            'detection_context': {
                'threshold': strategy.get('detection_threshold', 0.5),
                'feature_weights': strategy.get('feature_weights', torch.ones(len(sample.features))),
                'confidence_level': strategy.get('confidence_level', 0.8)
            },
            'available_info': {
                'has_true_label': False,  # 学生不知道真实标签
                'has_difficulty': False,  # 学生不知道真实难度
                'has_domain_info': True   # 学生知道领域信息
            }
        }
        
    def _create_adversary_view(self, sample: DataSample, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """创建对手视角"""
        return {
            'original_features': sample.features,
            'generation_context': {
                'anomaly_type': strategy.get('anomaly_type', 'stealth'),
                'intensity': strategy.get('intensity', 0.5),
                'target_features': strategy.get('target_features', []),
                'evasion_technique': strategy.get('evasion_technique', 'direct')
            },
            'available_info': {
                'has_true_label': True,   # 对手知道真实标签
                'has_difficulty': False,  # 对手不知道真实难度
                'has_domain_info': True   # 对手知道领域信息
            }
        }
        
    def update_usage_statistics(self, battle_result: Dict[str, Any]):
        """更新使用统计"""
        sample_id = battle_result.get('sample_id')
        student_success = battle_result.get('student_success', False)
        adversary_success = battle_result.get('adversary_success', False)
        
        if sample_id is not None:
            self.usage_stats[sample_id] += 1
            self.performance_stats[sample_id].append({
                'student_success': student_success,
                'adversary_success': adversary_success,
                'timestamp': battle_result.get('timestamp')
            })
            
    def get_dataset_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        return {
            'total_samples': len(self.raw_dataset),
            'partitions': {
                stage: {
                    'count': len(partition.samples),
                    'difficulty_range': partition.difficulty_range,
                    'anomaly_ratio': partition.anomaly_ratio
                }
                for stage, partition in self.partitions.items()
            },
            'usage_stats': dict(self.usage_stats),
            'difficulty_distribution': self._get_difficulty_distribution()
        }
        
    def _print_dataset_statistics(self):
        """打印数据集统计信息"""
        stats = self.get_dataset_statistics()
        
        print(f"📊 数据集统计:")
        print(f"   总样本数: {stats['total_samples']}")
        
        for stage, info in stats['partitions'].items():
            print(f"   {stage}: {info['count']} 样本, "
                  f"难度范围: {info['difficulty_range']}, "
                  f"异常比例: {info['anomaly_ratio']:.2%}")
                  
    def _get_next_stage(self, current_stage: str) -> str:
        """获取下一阶段"""
        stage_order = ['foundation', 'adversarial', 'mastery']
        try:
            current_idx = stage_order.index(current_stage)
            return stage_order[min(current_idx + 1, len(stage_order) - 1)]
        except ValueError:
            return 'foundation'
            
    def _get_previous_stage(self, current_stage: str) -> str:
        """获取上一阶段"""
        stage_order = ['foundation', 'adversarial', 'mastery']
        try:
            current_idx = stage_order.index(current_stage)
            return stage_order[max(current_idx - 1, 0)]
        except ValueError:
            return 'foundation'
            
    def _generate_recommendation_reasoning(self, student_perf: float, adversary_perf: float) -> str:
        """生成推荐理由"""
        if student_perf > 0.8 and adversary_perf < 0.6:
            return "学生表现优秀且对手相对较弱，建议增加难度以保持挑战性"
        elif student_perf < 0.4:
            return "学生表现不佳，建议降低难度以避免挫折感"
        else:
            return "当前难度适中，建议维持现状"
            
    def _recommend_samples_for_difficulty(self, target_difficulty: float) -> List[int]:
        """为特定难度推荐样本"""
        candidates = [
            s.metadata['id'] for s in self.raw_dataset
            if abs(s.difficulty - target_difficulty) < 0.1
        ]
        return candidates[:10]  # 返回前10个推荐
        
    def _get_general_dataset_info(self) -> Dict[str, Any]:
        """获取通用数据集信息"""
        return {
            'total_samples': len(self.raw_dataset),
            'domains': list(set(s.domain for s in self.raw_dataset)),
            'difficulty_range': (
                min(s.difficulty for s in self.raw_dataset),
                max(s.difficulty for s in self.raw_dataset)
            ),
            'anomaly_ratio': sum(1 for s in self.raw_dataset if s.label == 1) / len(self.raw_dataset)
        }
