"""
博弈智能体实现

实现教师-学生-对手三方博弈模型中的智能体：
1. TeacherAgent: 课程设计和训练协调
2. StudentAgent: 异常检测器学习智能体
3. AdversaryAgent: 异常生成对抗智能体
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum


class AgentType(Enum):
    """智能体类型"""
    TEACHER = "teacher"
    STUDENT = "student"
    ADVERSARY = "adversary"


@dataclass
class GameState:
    """博弈状态"""
    round_number: int
    student_performance: Dict[str, float]
    adversary_success_rate: float
    curriculum_stage: str
    difficulty_level: float
    historical_outcomes: List[Dict]


@dataclass
class AgentBelief:
    """智能体信念"""
    opponent_strategy_belief: torch.Tensor
    opponent_capability_belief: float
    uncertainty_level: float
    confidence: float


class BaseGameAgent(ABC):
    """基础博弈智能体抽象类"""

    def __init__(self, agent_id: str, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.config = config
        self.agent_type = self._get_agent_type()

        # 智能体状态
        self.beliefs = {}
        self.strategy_history = []
        self.performance_history = []

        # 学习参数
        self.learning_rate = config.get('learning_rate', 0.01)
        self.exploration_rate = config.get('exploration_rate', 0.1)
        self.belief_update_rate = config.get('belief_update_rate', 0.05)

    @abstractmethod
    def _get_agent_type(self) -> AgentType:
        """获取智能体类型"""
        pass

    @abstractmethod
    def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """选择策略"""
        pass

    @abstractmethod
    def update_beliefs(self, game_state: GameState, outcomes: Dict[str, Any]):
        """更新信念"""
        pass

    def get_performance_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        if not self.performance_history:
            return {'avg_performance': 0.0, 'trend': 0.0}

        recent_performance = self.performance_history[-10:]  # 最近10轮
        avg_performance = np.mean(recent_performance)

        if len(recent_performance) > 1:
            trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
        else:
            trend = 0.0

        return {
            'avg_performance': avg_performance,
            'trend': trend,
            'stability': 1.0 - np.std(recent_performance) if len(recent_performance) > 1 else 1.0
        }


class TeacherAgent(BaseGameAgent):
    """
    教师智能体

    负责课程设计、难度调节和训练协调。
    目标：最大化学生学习效果，优化训练效率。
    """

    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)

        # 课程设计参数
        self.curriculum_stages = ['foundation', 'adversarial', 'mastery']
        self.current_stage = 'foundation'
        self.stage_transition_thresholds = config.get('stage_thresholds', {
            'foundation_to_adversarial': 0.7,
            'adversarial_to_mastery': 0.8
        })

        # 自适应参数
        self.difficulty_adjustment_rate = config.get('difficulty_adjustment_rate', 0.1)
        self.patience_threshold = config.get('patience_threshold', 5)
        self.performance_window = config.get('performance_window', 10)

    def _get_agent_type(self) -> AgentType:
        return AgentType.TEACHER

    def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """选择教学策略"""

        # 分析当前状态
        student_metrics = self._analyze_student_performance(game_state)
        adversary_metrics = self._analyze_adversary_performance(game_state)

        # 决定课程阶段
        new_stage = self._determine_curriculum_stage(student_metrics, game_state)

        # 调整难度
        difficulty = self._adjust_difficulty(student_metrics, adversary_metrics, game_state)

        # 设计奖惩机制
        reward_structure = self._design_reward_structure(new_stage, difficulty)

        # 选择干预策略
        intervention = self._select_intervention(student_metrics, adversary_metrics)

        strategy = {
            'curriculum_stage': new_stage,
            'difficulty_level': difficulty,
            'reward_structure': reward_structure,
            'intervention': intervention,
            'feedback_frequency': self._determine_feedback_frequency(new_stage),
            'exploration_encouragement': self._calculate_exploration_bonus(student_metrics)
        }

        self.strategy_history.append(strategy)
        return strategy

    def _analyze_student_performance(self, game_state: GameState) -> Dict[str, float]:
        """分析学生表现"""
        student_perf = game_state.student_performance

        # 计算关键指标
        detection_accuracy = student_perf.get('detection_accuracy', 0.0)
        false_positive_rate = student_perf.get('false_positive_rate', 0.0)
        learning_speed = student_perf.get('learning_speed', 0.0)
        stability = student_perf.get('stability', 0.0)

        # 计算综合学习效果
        learning_effectiveness = (
            0.4 * detection_accuracy +
            0.2 * (1 - false_positive_rate) +
            0.2 * learning_speed +
            0.2 * stability
        )

        return {
            'detection_accuracy': detection_accuracy,
            'false_positive_rate': false_positive_rate,
            'learning_speed': learning_speed,
            'stability': stability,
            'learning_effectiveness': learning_effectiveness,
            'frustration_level': self._estimate_frustration(game_state),
            'boredom_level': self._estimate_boredom(game_state)
        }

    def _analyze_adversary_performance(self, game_state: GameState) -> Dict[str, float]:
        """分析对手表现"""
        return {
            'evasion_rate': game_state.adversary_success_rate,
            'strategy_diversity': self._calculate_strategy_diversity(game_state),
            'adaptation_speed': self._calculate_adaptation_speed(game_state)
        }

    def _determine_curriculum_stage(self, student_metrics: Dict[str, float],
                                  game_state: GameState) -> str:
        """确定课程阶段"""
        current_effectiveness = student_metrics['learning_effectiveness']
        current_stage = game_state.curriculum_stage

        # 阶段转换逻辑
        if current_stage == 'foundation':
            if current_effectiveness > self.stage_transition_thresholds['foundation_to_adversarial']:
                return 'adversarial'
        elif current_stage == 'adversarial':
            if current_effectiveness > self.stage_transition_thresholds['adversarial_to_mastery']:
                return 'mastery'
            elif current_effectiveness < 0.5:  # 退回基础阶段
                return 'foundation'
        elif current_stage == 'mastery':
            if current_effectiveness < 0.6:  # 退回对抗阶段
                return 'adversarial'

        return current_stage

    def _adjust_difficulty(self, student_metrics: Dict[str, float],
                          adversary_metrics: Dict[str, float],
                          game_state: GameState) -> float:
        """调整难度级别"""
        current_difficulty = game_state.difficulty_level

        # 基于学生表现调整
        if student_metrics['learning_effectiveness'] > 0.8:
            difficulty_delta = self.difficulty_adjustment_rate
        elif student_metrics['learning_effectiveness'] < 0.4:
            difficulty_delta = -self.difficulty_adjustment_rate
        else:
            difficulty_delta = 0.0

        # 考虑挫折感和无聊感
        if student_metrics['frustration_level'] > 0.7:
            difficulty_delta -= 0.05
        elif student_metrics['boredom_level'] > 0.7:
            difficulty_delta += 0.05

        # 考虑对手表现
        if adversary_metrics['evasion_rate'] > 0.8:
            difficulty_delta -= 0.02  # 对手太强，降低难度
        elif adversary_metrics['evasion_rate'] < 0.2:
            difficulty_delta += 0.02  # 对手太弱，增加难度

        new_difficulty = np.clip(current_difficulty + difficulty_delta, 0.1, 1.0)
        return new_difficulty

    def _design_reward_structure(self, stage: str, difficulty: float) -> Dict[str, float]:
        """设计奖惩结构"""
        # 获取当前数据集特性
        dataset_characteristics = self._analyze_current_dataset()

        # 基础奖励结构
        base_rewards = self._get_base_rewards_for_stage(stage)

        # 根据数据集特性调整奖励
        dataset_adjusted_rewards = self._adjust_rewards_for_dataset(base_rewards, dataset_characteristics)

        # 根据难度进一步调整
        final_rewards = self._apply_difficulty_adjustment(dataset_adjusted_rewards, difficulty)

        return final_rewards

    def _analyze_current_dataset(self) -> Dict[str, Any]:
        """分析当前数据集特性"""
        # 这里应该从数据集管理器获取当前数据集信息
        # 暂时返回默认特性，实际实现中应该与数据集管理器集成
        return {
            'dataset_type': 'knowledge_tracing',  # 数据集类型
            'domain_complexity': 0.6,            # 领域复杂度
            'feature_dimensionality': 'medium',   # 特征维度
            'temporal_dependency': True,          # 时序依赖性
            'sparsity_level': 0.3,               # 稀疏程度
            'noise_level': 0.1,                  # 噪声水平
            'class_imbalance': 0.2,              # 类别不平衡程度
            'adversarial_vulnerability': 0.5      # 对抗脆弱性
        }

    def _get_base_rewards_for_stage(self, stage: str) -> Dict[str, float]:
        """获取阶段基础奖励结构"""
        stage_rewards = {
            'foundation': {
                'detection_accuracy': 0.4,      # 检测准确率权重
                'learning_progress': 0.3,       # 学习进展权重
                'stability': 0.2,               # 稳定性权重
                'exploration': 0.1,             # 探索奖励权重
                'false_positive_penalty': -0.2  # 误报惩罚
            },
            'adversarial': {
                'detection_accuracy': 0.3,
                'robustness': 0.3,              # 鲁棒性权重
                'adaptation_speed': 0.2,        # 适应速度权重
                'strategic_thinking': 0.1,      # 策略思维权重
                'false_positive_penalty': -0.3
            },
            'mastery': {
                'detection_accuracy': 0.25,
                'robustness': 0.25,
                'generalization': 0.25,         # 泛化能力权重
                'innovation': 0.15,             # 创新性权重
                'efficiency': 0.1,              # 效率权重
                'false_positive_penalty': -0.4
            }
        }
        return stage_rewards.get(stage, stage_rewards['foundation'])

    def _adjust_rewards_for_dataset(self, base_rewards: Dict[str, float],
                                   characteristics: Dict[str, Any]) -> Dict[str, float]:
        """根据数据集特性调整奖励结构"""
        adjusted_rewards = base_rewards.copy()

        # 根据数据集类型调整
        dataset_type = characteristics.get('dataset_type', 'general')
        if dataset_type == 'knowledge_tracing':
            # 知识追踪：更重视学习进展和稳定性
            adjusted_rewards['learning_progress'] = adjusted_rewards.get('learning_progress', 0.2) * 1.3
            adjusted_rewards['stability'] = adjusted_rewards.get('stability', 0.2) * 1.2

        elif dataset_type == 'cybersecurity':
            # 网络安全：更重视鲁棒性和快速响应
            adjusted_rewards['robustness'] = adjusted_rewards.get('robustness', 0.2) * 1.4
            adjusted_rewards['adaptation_speed'] = adjusted_rewards.get('adaptation_speed', 0.2) * 1.3

        elif dataset_type == 'financial':
            # 金融：更重视准确性和稳定性，严厉惩罚误报
            adjusted_rewards['detection_accuracy'] *= 1.2
            adjusted_rewards['stability'] = adjusted_rewards.get('stability', 0.2) * 1.3
            adjusted_rewards['false_positive_penalty'] *= 1.5

        # 根据领域复杂度调整
        complexity = characteristics.get('domain_complexity', 0.5)
        if complexity > 0.7:  # 高复杂度
            adjusted_rewards['strategic_thinking'] = adjusted_rewards.get('strategic_thinking', 0.1) * 1.5
            adjusted_rewards['generalization'] = adjusted_rewards.get('generalization', 0.2) * 1.3

        # 根据时序依赖性调整
        if characteristics.get('temporal_dependency', False):
            adjusted_rewards['adaptation_speed'] = adjusted_rewards.get('adaptation_speed', 0.2) * 1.2

        # 根据对抗脆弱性调整
        vulnerability = characteristics.get('adversarial_vulnerability', 0.5)
        if vulnerability > 0.6:  # 高脆弱性
            adjusted_rewards['robustness'] = adjusted_rewards.get('robustness', 0.2) * 1.4

        return adjusted_rewards

    def _apply_difficulty_adjustment(self, rewards: Dict[str, float],
                                   difficulty: float) -> Dict[str, float]:
        """根据难度级别调整奖励"""
        adjusted_rewards = rewards.copy()

        # 高难度时更重视鲁棒性和创新性
        if difficulty > 0.7:
            adjusted_rewards['robustness'] = adjusted_rewards.get('robustness', 0.2) * 1.2
            adjusted_rewards['innovation'] = adjusted_rewards.get('innovation', 0.1) * 1.3

        # 低难度时更重视基础能力
        elif difficulty < 0.4:
            adjusted_rewards['detection_accuracy'] *= 1.2
            adjusted_rewards['learning_progress'] = adjusted_rewards.get('learning_progress', 0.2) * 1.1

        return adjusted_rewards

    def design_dataset_specific_curriculum(self, dataset_profile: Dict[str, Any],
                                         game_state: GameState) -> Dict[str, Any]:
        """设计数据集特定的课程策略"""
        dataset_type = dataset_profile.get('dataset_type', 'general')

        if dataset_type == 'knowledge_tracing':
            return self._design_knowledge_tracing_curriculum(dataset_profile, game_state)
        elif dataset_type == 'cybersecurity':
            return self._design_cybersecurity_curriculum(dataset_profile, game_state)
        elif dataset_type == 'financial':
            return self._design_financial_curriculum(dataset_profile, game_state)
        elif dataset_type == 'healthcare':
            return self._design_healthcare_curriculum(dataset_profile, game_state)
        else:
            return self._design_general_curriculum(dataset_profile, game_state)

    def _design_knowledge_tracing_curriculum(self, dataset_profile: Dict[str, Any],
                                           game_state: GameState) -> Dict[str, Any]:
        """设计知识追踪特定的课程策略"""
        curriculum = {
            'curriculum_philosophy': 'mastery_based_progression',
            'stage_design': {
                'foundation': {
                    'focus': 'basic_pattern_recognition',
                    'anomaly_types': ['simple_guessing', 'random_responses'],
                    'difficulty_progression': 'gradual_increase',
                    'prerequisite_checking': True,
                    'mastery_threshold': 0.75,
                    'intervention_triggers': {
                        'confusion_detection': 0.6,
                        'disengagement_detection': 0.7
                    }
                },
                'adversarial': {
                    'focus': 'sophisticated_cheating_detection',
                    'anomaly_types': ['strategic_guessing', 'collaboration', 'external_help'],
                    'difficulty_progression': 'adaptive_spiral',
                    'temporal_modeling': True,
                    'mastery_threshold': 0.8,
                    'intervention_triggers': {
                        'adaptation_slowness': 0.5,
                        'pattern_blindness': 0.6
                    }
                },
                'mastery': {
                    'focus': 'novel_anomaly_generalization',
                    'anomaly_types': ['unseen_patterns', 'cross_domain_transfer'],
                    'difficulty_progression': 'challenge_based',
                    'creativity_encouragement': True,
                    'mastery_threshold': 0.85,
                    'intervention_triggers': {
                        'innovation_stagnation': 0.4
                    }
                }
            },
            'special_considerations': {
                'skill_dependency_modeling': True,
                'forgetting_curve_awareness': True,
                'individual_learning_pace': True,
                'metacognitive_skill_development': True
            }
        }

        # 根据当前博弈状态调整
        current_stage = game_state.curriculum_stage
        if current_stage in curriculum['stage_design']:
            stage_config = curriculum['stage_design'][current_stage]

            # 根据学生表现调整阈值
            student_performance = game_state.student_performance.get('detection_accuracy', 0.5)
            if student_performance < 0.4:
                stage_config['mastery_threshold'] *= 0.9  # 降低要求
                stage_config['intervention_triggers'] = {k: v * 0.8 for k, v in stage_config['intervention_triggers'].items()}
            elif student_performance > 0.9:
                stage_config['mastery_threshold'] *= 1.1  # 提高要求

        return curriculum

    def _design_cybersecurity_curriculum(self, dataset_profile: Dict[str, Any],
                                       game_state: GameState) -> Dict[str, Any]:
        """设计网络安全特定的课程策略"""
        curriculum = {
            'curriculum_philosophy': 'threat_adaptive_training',
            'stage_design': {
                'foundation': {
                    'focus': 'signature_based_detection',
                    'anomaly_types': ['known_malware', 'simple_intrusions'],
                    'difficulty_progression': 'threat_level_based',
                    'real_time_requirements': True,
                    'mastery_threshold': 0.8,  # 更高要求
                    'intervention_triggers': {
                        'false_positive_rate': 0.1,
                        'detection_latency': 0.5
                    }
                },
                'adversarial': {
                    'focus': 'evasion_resistant_detection',
                    'anomaly_types': ['polymorphic_attacks', 'zero_day_exploits'],
                    'difficulty_progression': 'adversarial_escalation',
                    'adaptive_defense': True,
                    'mastery_threshold': 0.85,
                    'intervention_triggers': {
                        'evasion_vulnerability': 0.3,
                        'adaptation_lag': 0.4
                    }
                },
                'mastery': {
                    'focus': 'proactive_threat_hunting',
                    'anomaly_types': ['apt_campaigns', 'insider_threats'],
                    'difficulty_progression': 'intelligence_driven',
                    'threat_intelligence_integration': True,
                    'mastery_threshold': 0.9,
                    'intervention_triggers': {
                        'threat_prediction_accuracy': 0.6
                    }
                }
            },
            'special_considerations': {
                'real_time_constraints': True,
                'false_positive_minimization': True,
                'threat_landscape_evolution': True,
                'multi_vector_attack_handling': True
            }
        }

        # 根据威胁级别调整
        threat_level = dataset_profile.get('threat_level', 'medium')
        if threat_level == 'high':
            for stage in curriculum['stage_design'].values():
                stage['mastery_threshold'] *= 1.1
                stage['intervention_triggers'] = {k: v * 0.8 for k, v in stage['intervention_triggers'].items()}

        return curriculum

    def _design_financial_curriculum(self, dataset_profile: Dict[str, Any],
                                   game_state: GameState) -> Dict[str, Any]:
        """设计金融风控特定的课程策略"""
        curriculum = {
            'curriculum_philosophy': 'risk_balanced_learning',
            'stage_design': {
                'foundation': {
                    'focus': 'rule_based_fraud_detection',
                    'anomaly_types': ['transaction_anomalies', 'account_takeovers'],
                    'difficulty_progression': 'risk_weighted',
                    'regulatory_compliance': True,
                    'mastery_threshold': 0.85,  # 金融要求更高
                    'intervention_triggers': {
                        'false_positive_cost': 0.05,  # 严格控制误报
                        'regulatory_violation_risk': 0.1
                    }
                },
                'adversarial': {
                    'focus': 'sophisticated_fraud_schemes',
                    'anomaly_types': ['money_laundering', 'synthetic_identities'],
                    'difficulty_progression': 'fraud_sophistication_based',
                    'behavioral_analysis': True,
                    'mastery_threshold': 0.9,
                    'intervention_triggers': {
                        'fraud_loss_ratio': 0.02,
                        'investigation_efficiency': 0.7
                    }
                },
                'mastery': {
                    'focus': 'emerging_fraud_prediction',
                    'anomaly_types': ['novel_schemes', 'cross_channel_fraud'],
                    'difficulty_progression': 'predictive_modeling',
                    'market_trend_integration': True,
                    'mastery_threshold': 0.92,
                    'intervention_triggers': {
                        'prediction_accuracy': 0.8
                    }
                }
            },
            'special_considerations': {
                'regulatory_compliance': True,
                'cost_benefit_optimization': True,
                'customer_experience_preservation': True,
                'cross_channel_correlation': True
            }
        }

        # 根据监管环境调整
        regulatory_strictness = dataset_profile.get('regulatory_strictness', 'medium')
        if regulatory_strictness == 'high':
            for stage in curriculum['stage_design'].values():
                stage['mastery_threshold'] *= 1.05
                if 'false_positive_cost' in stage['intervention_triggers']:
                    stage['intervention_triggers']['false_positive_cost'] *= 0.5

        return curriculum

    def _design_healthcare_curriculum(self, dataset_profile: Dict[str, Any],
                                    game_state: GameState) -> Dict[str, Any]:
        """设计医疗健康特定的课程策略"""
        curriculum = {
            'curriculum_philosophy': 'patient_safety_first',
            'stage_design': {
                'foundation': {
                    'focus': 'clinical_anomaly_recognition',
                    'anomaly_types': ['vital_sign_anomalies', 'medication_errors'],
                    'difficulty_progression': 'severity_based',
                    'patient_safety_priority': True,
                    'mastery_threshold': 0.9,  # 医疗要求最高
                    'intervention_triggers': {
                        'life_threatening_miss_rate': 0.01,  # 极低容忍度
                        'false_alarm_fatigue': 0.3
                    }
                },
                'adversarial': {
                    'focus': 'complex_syndrome_detection',
                    'anomaly_types': ['rare_diseases', 'drug_interactions'],
                    'difficulty_progression': 'clinical_complexity_based',
                    'multi_modal_integration': True,
                    'mastery_threshold': 0.92,
                    'intervention_triggers': {
                        'diagnostic_accuracy': 0.85,
                        'time_to_diagnosis': 0.7
                    }
                },
                'mastery': {
                    'focus': 'predictive_health_monitoring',
                    'anomaly_types': ['early_deterioration', 'epidemic_patterns'],
                    'difficulty_progression': 'predictive_modeling',
                    'population_health_awareness': True,
                    'mastery_threshold': 0.95,
                    'intervention_triggers': {
                        'early_warning_sensitivity': 0.9
                    }
                }
            },
            'special_considerations': {
                'patient_privacy_protection': True,
                'clinical_workflow_integration': True,
                'regulatory_compliance': True,
                'interpretability_requirements': True
            }
        }

        # 根据医疗环境调整
        clinical_setting = dataset_profile.get('clinical_setting', 'general')
        if clinical_setting == 'icu':
            for stage in curriculum['stage_design'].values():
                stage['mastery_threshold'] *= 1.02  # ICU要求更高
                if 'life_threatening_miss_rate' in stage['intervention_triggers']:
                    stage['intervention_triggers']['life_threatening_miss_rate'] *= 0.5

        return curriculum

    def _design_general_curriculum(self, dataset_profile: Dict[str, Any],
                                 game_state: GameState) -> Dict[str, Any]:
        """设计通用的课程策略"""
        curriculum = {
            'curriculum_philosophy': 'balanced_progressive_learning',
            'stage_design': {
                'foundation': {
                    'focus': 'basic_anomaly_detection',
                    'anomaly_types': ['statistical_outliers', 'simple_patterns'],
                    'difficulty_progression': 'linear_increase',
                    'mastery_threshold': 0.7,
                    'intervention_triggers': {
                        'learning_plateau': 0.6,
                        'confusion_level': 0.7
                    }
                },
                'adversarial': {
                    'focus': 'robust_detection',
                    'anomaly_types': ['adversarial_examples', 'concept_drift'],
                    'difficulty_progression': 'adaptive_increase',
                    'mastery_threshold': 0.8,
                    'intervention_triggers': {
                        'robustness_degradation': 0.5,
                        'adaptation_difficulty': 0.6
                    }
                },
                'mastery': {
                    'focus': 'generalization_and_transfer',
                    'anomaly_types': ['novel_patterns', 'cross_domain_anomalies'],
                    'difficulty_progression': 'challenge_driven',
                    'mastery_threshold': 0.85,
                    'intervention_triggers': {
                        'transfer_learning_failure': 0.4
                    }
                }
            },
            'special_considerations': {
                'domain_agnostic_learning': True,
                'transfer_learning_support': True,
                'scalability_awareness': True
            }
        }

        # 根据数据集复杂度调整
        complexity = dataset_profile.get('domain_complexity', 0.5)
        if complexity > 0.7:
            for stage in curriculum['stage_design'].values():
                stage['mastery_threshold'] *= 1.1
        elif complexity < 0.3:
            for stage in curriculum['stage_design'].values():
                stage['mastery_threshold'] *= 0.95

        return curriculum

    def adapt_curriculum_to_performance(self, curriculum: Dict[str, Any],
                                      performance_history: List[Dict]) -> Dict[str, Any]:
        """根据性能历史自适应调整课程"""
        if not performance_history:
            return curriculum

        adapted_curriculum = curriculum.copy()
        recent_performance = performance_history[-10:] if len(performance_history) >= 10 else performance_history

        # 计算性能趋势
        accuracy_trend = self._calculate_performance_trend(recent_performance, 'detection_accuracy')
        learning_speed = self._calculate_learning_speed(recent_performance)
        stability = self._calculate_performance_stability(recent_performance)

        # 根据趋势调整课程
        if accuracy_trend < -0.1:  # 性能下降
            adapted_curriculum = self._apply_remedial_adjustments(adapted_curriculum)
        elif accuracy_trend > 0.1 and learning_speed > 0.8:  # 快速进步
            adapted_curriculum = self._apply_acceleration_adjustments(adapted_curriculum)

        if stability < 0.3:  # 不稳定
            adapted_curriculum = self._apply_stability_adjustments(adapted_curriculum)

        return adapted_curriculum

    def _calculate_performance_trend(self, performance_history: List[Dict], metric: str) -> float:
        """计算性能趋势"""
        if len(performance_history) < 3:
            return 0.0

        values = [p.get(metric, 0.0) for p in performance_history]
        x = np.arange(len(values))
        trend = np.polyfit(x, values, 1)[0]
        return trend

    def _calculate_learning_speed(self, performance_history: List[Dict]) -> float:
        """计算学习速度"""
        if len(performance_history) < 2:
            return 0.5

        improvements = []
        for i in range(1, len(performance_history)):
            prev_acc = performance_history[i-1].get('detection_accuracy', 0.0)
            curr_acc = performance_history[i].get('detection_accuracy', 0.0)
            improvement = max(0, curr_acc - prev_acc)
            improvements.append(improvement)

        return np.mean(improvements) if improvements else 0.0

    def _calculate_performance_stability(self, performance_history: List[Dict]) -> float:
        """计算性能稳定性"""
        if len(performance_history) < 3:
            return 1.0

        accuracies = [p.get('detection_accuracy', 0.0) for p in performance_history]
        stability = 1.0 - np.std(accuracies)
        return max(0.0, stability)

    def _apply_remedial_adjustments(self, curriculum: Dict[str, Any]) -> Dict[str, Any]:
        """应用补救性调整"""
        adjusted = curriculum.copy()

        # 降低难度要求
        for stage_name, stage_config in adjusted['stage_design'].items():
            stage_config['mastery_threshold'] *= 0.9
            stage_config['difficulty_progression'] = 'gentle_increase'

            # 增加干预触发敏感度
            for trigger, threshold in stage_config['intervention_triggers'].items():
                stage_config['intervention_triggers'][trigger] = threshold * 0.8

        return adjusted

    def _apply_acceleration_adjustments(self, curriculum: Dict[str, Any]) -> Dict[str, Any]:
        """应用加速调整"""
        adjusted = curriculum.copy()

        # 提高难度要求
        for stage_name, stage_config in adjusted['stage_design'].items():
            stage_config['mastery_threshold'] *= 1.1
            stage_config['difficulty_progression'] = 'accelerated_increase'

        return adjusted

    def _apply_stability_adjustments(self, curriculum: Dict[str, Any]) -> Dict[str, Any]:
        """应用稳定性调整"""
        adjusted = curriculum.copy()

        # 增加稳定性支持
        for stage_name, stage_config in adjusted['stage_design'].items():
            stage_config['stability_support'] = True
            stage_config['difficulty_progression'] = 'plateau_aware'

        return adjusted

    def _select_intervention(self, student_metrics: Dict[str, float],
                           adversary_metrics: Dict[str, float]) -> Dict[str, Any]:
        """选择干预策略"""
        intervention = {
            'type': 'none',
            'intensity': 0.0,
            'duration': 0
        }

        # 基于学生困难程度决定干预
        if student_metrics['frustration_level'] > 0.8:
            intervention = {
                'type': 'hint_provision',
                'intensity': 0.7,
                'duration': 3
            }
        elif student_metrics['learning_speed'] < 0.3:
            intervention = {
                'type': 'strategy_guidance',
                'intensity': 0.5,
                'duration': 5
            }
        elif student_metrics['boredom_level'] > 0.8:
            intervention = {
                'type': 'challenge_boost',
                'intensity': 0.6,
                'duration': 2
            }

        return intervention

    def _estimate_frustration(self, game_state: GameState) -> float:
        """估计学生挫折感"""
        recent_outcomes = game_state.historical_outcomes[-5:]  # 最近5轮
        if not recent_outcomes:
            return 0.0

        failure_rate = sum(1 for outcome in recent_outcomes
                          if outcome.get('student_success', True) == False) / len(recent_outcomes)

        return min(1.0, failure_rate * 1.5)  # 挫折感与失败率正相关

    def _estimate_boredom(self, game_state: GameState) -> float:
        """估计学生无聊感"""
        recent_outcomes = game_state.historical_outcomes[-10:]  # 最近10轮
        if len(recent_outcomes) < 5:
            return 0.0

        success_rate = sum(1 for outcome in recent_outcomes
                          if outcome.get('student_success', False) == True) / len(recent_outcomes)

        # 成功率过高且难度没有提升时产生无聊感
        if success_rate > 0.9 and game_state.difficulty_level < 0.7:
            return min(1.0, (success_rate - 0.8) * 2)

        return 0.0

    def _determine_feedback_frequency(self, stage: str) -> float:
        """确定反馈频率"""
        feedback_frequencies = {
            'foundation': 1.0,    # 每轮都给反馈
            'adversarial': 0.7,   # 70%的轮次给反馈
            'mastery': 0.3        # 30%的轮次给反馈
        }
        return feedback_frequencies.get(stage, 0.5)

    def _calculate_exploration_bonus(self, student_metrics: Dict[str, float]) -> float:
        """计算探索奖励"""
        # 当学生表现稳定时，鼓励探索新策略
        if student_metrics['stability'] > 0.8:
            return 0.2
        return 0.0

    def _calculate_strategy_diversity(self, game_state: GameState) -> float:
        """计算对手策略多样性"""
        # 简化实现：基于历史结果的方差
        recent_outcomes = game_state.historical_outcomes[-10:]
        if len(recent_outcomes) < 3:
            return 0.5

        success_rates = [outcome.get('adversary_success', 0.0) for outcome in recent_outcomes]
        diversity = np.std(success_rates)
        return min(1.0, diversity * 2)

    def _calculate_adaptation_speed(self, game_state: GameState) -> float:
        """计算对手适应速度"""
        # 简化实现：基于成功率变化趋势
        recent_outcomes = game_state.historical_outcomes[-5:]
        if len(recent_outcomes) < 3:
            return 0.5

        success_rates = [outcome.get('adversary_success', 0.0) for outcome in recent_outcomes]
        if len(success_rates) > 1:
            trend = np.polyfit(range(len(success_rates)), success_rates, 1)[0]
            return min(1.0, abs(trend) * 5)

        return 0.5

    def update_beliefs(self, game_state: GameState, outcomes: Dict[str, Any]):
        """更新对学生和对手的信念"""
        # 更新对学生能力的信念
        student_performance = outcomes.get('student_performance', 0.0)
        if 'student_capability' not in self.beliefs:
            self.beliefs['student_capability'] = student_performance
        else:
            self.beliefs['student_capability'] = (
                (1 - self.belief_update_rate) * self.beliefs['student_capability'] +
                self.belief_update_rate * student_performance
            )

        # 更新对对手能力的信念
        adversary_performance = outcomes.get('adversary_performance', 0.0)
        if 'adversary_capability' not in self.beliefs:
            self.beliefs['adversary_capability'] = adversary_performance
        else:
            self.beliefs['adversary_capability'] = (
                (1 - self.belief_update_rate) * self.beliefs['adversary_capability'] +
                self.belief_update_rate * adversary_performance
            )

        # 记录性能
        teaching_effectiveness = outcomes.get('teaching_effectiveness', 0.0)
        self.performance_history.append(teaching_effectiveness)


class StudentAgent(BaseGameAgent):
    """
    学生智能体 (异常检测器)

    负责学习异常检测策略，在与对手的博弈中不断改进。
    目标：最大化检测准确率，最小化误报率。
    """

    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)

        # 检测器参数
        self.detection_threshold = config.get('initial_threshold', 0.5)
        self.feature_weights = torch.ones(config.get('feature_dim', 10))
        self.strategy_memory = []

        # 学习参数
        self.adaptation_rate = config.get('adaptation_rate', 0.1)
        self.memory_size = config.get('memory_size', 100)
        self.confidence_threshold = config.get('confidence_threshold', 0.8)

        # 对手建模
        self.adversary_strategy_model = self._initialize_adversary_model()

    def _get_agent_type(self) -> AgentType:
        return AgentType.STUDENT

    def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """选择检测策略"""

        # 分析当前博弈状态
        threat_assessment = self._assess_threat_level(game_state)

        # 预测对手策略
        predicted_adversary_strategy = self._predict_adversary_strategy(game_state)

        # 选择最佳响应策略
        detection_strategy = self._compute_best_response(
            predicted_adversary_strategy, threat_assessment, game_state
        )

        # 添加探索性
        if np.random.random() < self.exploration_rate:
            detection_strategy = self._add_exploration(detection_strategy)

        strategy = {
            'detection_threshold': detection_strategy['threshold'],
            'feature_weights': detection_strategy['weights'],
            'confidence_level': detection_strategy['confidence'],
            'strategy_type': detection_strategy['type'],
            'exploration_factor': self.exploration_rate
        }

        self.strategy_history.append(strategy)
        return strategy

    def _assess_threat_level(self, game_state: GameState) -> Dict[str, float]:
        """评估威胁级别"""
        # 基于历史对手成功率评估威胁
        recent_adversary_success = game_state.adversary_success_rate

        # 基于课程阶段评估威胁
        stage_threat_levels = {
            'foundation': 0.3,
            'adversarial': 0.7,
            'mastery': 0.9
        }
        stage_threat = stage_threat_levels.get(game_state.curriculum_stage, 0.5)

        # 综合威胁评估
        overall_threat = 0.6 * recent_adversary_success + 0.4 * stage_threat

        return {
            'overall_threat': overall_threat,
            'adversary_threat': recent_adversary_success,
            'curriculum_threat': stage_threat,
            'uncertainty': self._calculate_uncertainty(game_state)
        }

    def _predict_adversary_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """预测对手策略"""
        # 基于历史数据预测对手行为
        recent_outcomes = game_state.historical_outcomes[-5:]

        if not recent_outcomes:
            return {'predicted_intensity': 0.5, 'predicted_type': 'random', 'confidence': 0.3}

        # 分析对手策略模式
        adversary_intensities = [outcome.get('adversary_intensity', 0.5) for outcome in recent_outcomes]
        adversary_types = [outcome.get('adversary_type', 'random') for outcome in recent_outcomes]

        # 预测强度
        predicted_intensity = np.mean(adversary_intensities)

        # 预测类型（最常见的类型）
        from collections import Counter
        type_counts = Counter(adversary_types)
        predicted_type = type_counts.most_common(1)[0][0] if type_counts else 'random'

        # 计算预测置信度
        intensity_variance = np.var(adversary_intensities)
        confidence = max(0.1, 1.0 - intensity_variance)

        return {
            'predicted_intensity': predicted_intensity,
            'predicted_type': predicted_type,
            'confidence': confidence,
            'trend': self._calculate_adversary_trend(adversary_intensities)
        }

    def _compute_best_response(self, predicted_adversary: Dict[str, Any],
                             threat_assessment: Dict[str, float],
                             game_state: GameState) -> Dict[str, Any]:
        """计算最佳响应策略"""

        # 基于威胁级别调整检测阈值
        base_threshold = self.detection_threshold
        threat_level = threat_assessment['overall_threat']

        # 威胁越高，阈值越低（更敏感）
        adjusted_threshold = base_threshold * (1.0 - 0.3 * threat_level)
        adjusted_threshold = np.clip(adjusted_threshold, 0.1, 0.9)

        # 基于预测的对手策略调整特征权重
        predicted_intensity = predicted_adversary['predicted_intensity']
        predicted_type = predicted_adversary['predicted_type']

        # 调整特征权重
        adjusted_weights = self.feature_weights.clone()
        if predicted_type == 'stealth':  # 隐蔽型攻击
            adjusted_weights *= torch.tensor([1.2, 1.1, 0.9, 1.0, 1.1, 1.0, 0.8, 1.2, 1.0, 1.1])
        elif predicted_type == 'aggressive':  # 激进型攻击
            adjusted_weights *= torch.tensor([0.9, 1.2, 1.3, 1.1, 0.8, 1.2, 1.1, 0.9, 1.2, 1.0])

        # 归一化权重
        adjusted_weights = adjusted_weights / adjusted_weights.sum() * len(adjusted_weights)

        # 计算策略置信度
        confidence = predicted_adversary['confidence'] * (1.0 - threat_assessment['uncertainty'])

        # 确定策略类型
        if threat_level > 0.7:
            strategy_type = 'defensive'
        elif threat_level < 0.3:
            strategy_type = 'balanced'
        else:
            strategy_type = 'adaptive'

        return {
            'threshold': adjusted_threshold,
            'weights': adjusted_weights,
            'confidence': confidence,
            'type': strategy_type
        }

    def _add_exploration(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """添加探索性"""
        # 随机调整阈值
        threshold_noise = np.random.normal(0, 0.05)
        strategy['threshold'] = np.clip(
            strategy['threshold'] + threshold_noise, 0.1, 0.9
        )

        # 随机调整权重
        weight_noise = torch.normal(0, 0.02, strategy['weights'].shape)
        strategy['weights'] = torch.clamp(
            strategy['weights'] + weight_noise, 0.1, 2.0
        )

        return strategy

    def _calculate_uncertainty(self, game_state: GameState) -> float:
        """计算不确定性"""
        recent_outcomes = game_state.historical_outcomes[-10:]
        if len(recent_outcomes) < 3:
            return 0.8  # 高不确定性

        # 基于结果方差计算不确定性
        success_rates = [outcome.get('student_success', 0.0) for outcome in recent_outcomes]
        uncertainty = np.std(success_rates)

        return min(1.0, uncertainty * 2)

    def _calculate_adversary_trend(self, intensities: List[float]) -> float:
        """计算对手强度趋势"""
        if len(intensities) < 2:
            return 0.0

        # 计算线性趋势
        x = np.arange(len(intensities))
        trend = np.polyfit(x, intensities, 1)[0]

        return trend

    def _initialize_adversary_model(self) -> Dict[str, Any]:
        """初始化对手模型"""
        return {
            'strategy_patterns': {},
            'success_patterns': {},
            'adaptation_patterns': {}
        }

    def update_beliefs(self, game_state: GameState, outcomes: Dict[str, Any]):
        """更新对对手的信念"""
        # 更新对手策略信念
        adversary_strategy = outcomes.get('adversary_strategy', {})
        detection_result = outcomes.get('detection_result', False)

        # 更新对手能力估计
        if 'adversary_capability' not in self.beliefs:
            self.beliefs['adversary_capability'] = outcomes.get('adversary_success', 0.5)
        else:
            adversary_success = outcomes.get('adversary_success', 0.5)
            self.beliefs['adversary_capability'] = (
                (1 - self.belief_update_rate) * self.beliefs['adversary_capability'] +
                self.belief_update_rate * adversary_success
            )

        # 更新策略有效性
        strategy_effectiveness = 1.0 if detection_result else 0.0
        self.performance_history.append(strategy_effectiveness)

        # 自适应调整参数
        if len(self.performance_history) > 10:
            recent_performance = np.mean(self.performance_history[-10:])
            if recent_performance < 0.4:  # 表现不佳，增加探索
                self.exploration_rate = min(0.3, self.exploration_rate + 0.02)
            elif recent_performance > 0.8:  # 表现良好，减少探索
                self.exploration_rate = max(0.05, self.exploration_rate - 0.01)


class AdversaryAgent(BaseGameAgent):
    """
    对手智能体 (异常生成器)

    负责生成具有挑战性的异常，试图欺骗检测器。
    目标：最大化逃避检测的成功率，提高检测器的鲁棒性。
    """

    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)

        # 生成器参数
        self.anomaly_types = config.get('anomaly_types', ['stealth', 'aggressive', 'adaptive'])
        self.intensity_range = config.get('intensity_range', (0.1, 1.0))
        self.generation_strategies = []

        # 学习参数
        self.adaptation_speed = config.get('adaptation_speed', 0.15)
        self.creativity_factor = config.get('creativity_factor', 0.2)
        self.student_model_accuracy = config.get('student_model_accuracy', 0.7)

        # 学生建模
        self.student_strategy_model = self._initialize_student_model()
        self.success_memory = []

    def _get_agent_type(self) -> AgentType:
        return AgentType.ADVERSARY

    def select_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """选择对抗策略"""

        # 分析学生检测器状态
        student_analysis = self._analyze_student_detector(game_state)

        # 预测学生策略
        predicted_student_strategy = self._predict_student_strategy(game_state)

        # 选择最佳对抗策略
        adversarial_strategy = self._compute_adversarial_strategy(
            student_analysis, predicted_student_strategy, game_state
        )

        # 添加创新性
        if np.random.random() < self.creativity_factor:
            adversarial_strategy = self._add_creativity(adversarial_strategy)

        strategy = {
            'anomaly_type': adversarial_strategy['type'],
            'intensity': adversarial_strategy['intensity'],
            'target_features': adversarial_strategy['targets'],
            'evasion_technique': adversarial_strategy['technique'],
            'generation_parameters': adversarial_strategy['parameters']
        }

        self.strategy_history.append(strategy)
        return strategy

    def _analyze_student_detector(self, game_state: GameState) -> Dict[str, float]:
        """分析学生检测器的特点"""
        student_perf = game_state.student_performance

        # 分析检测器的弱点
        detection_accuracy = student_perf.get('detection_accuracy', 0.5)
        false_positive_rate = student_perf.get('false_positive_rate', 0.1)
        adaptation_speed = student_perf.get('learning_speed', 0.5)

        # 识别可利用的弱点
        exploitable_weaknesses = {
            'threshold_sensitivity': 1.0 - detection_accuracy,
            'false_positive_vulnerability': false_positive_rate,
            'slow_adaptation': 1.0 - adaptation_speed,
            'pattern_recognition_weakness': self._assess_pattern_weakness(game_state)
        }

        return exploitable_weaknesses

    def _predict_student_strategy(self, game_state: GameState) -> Dict[str, Any]:
        """预测学生检测策略"""
        recent_outcomes = game_state.historical_outcomes[-5:]

        if not recent_outcomes:
            return {
                'predicted_threshold': 0.5,
                'predicted_sensitivity': 0.5,
                'predicted_adaptation': 'slow',
                'confidence': 0.3
            }

        # 分析学生的检测模式
        student_successes = [outcome.get('student_success', False) for outcome in recent_outcomes]
        detection_patterns = [outcome.get('detection_pattern', 'random') for outcome in recent_outcomes]

        # 预测检测阈值
        success_rate = np.mean(student_successes)
        if success_rate > 0.8:
            predicted_threshold = 0.3  # 学生表现好，可能阈值较低
        elif success_rate < 0.4:
            predicted_threshold = 0.7  # 学生表现差，可能阈值较高
        else:
            predicted_threshold = 0.5

        # 预测敏感性
        predicted_sensitivity = success_rate

        # 预测适应性
        if len(set(detection_patterns)) > 2:
            predicted_adaptation = 'fast'
        else:
            predicted_adaptation = 'slow'

        # 计算预测置信度
        confidence = min(0.9, 0.3 + 0.1 * len(recent_outcomes))

        return {
            'predicted_threshold': predicted_threshold,
            'predicted_sensitivity': predicted_sensitivity,
            'predicted_adaptation': predicted_adaptation,
            'confidence': confidence
        }

    def _compute_adversarial_strategy(self, student_analysis: Dict[str, float],
                                    predicted_student: Dict[str, Any],
                                    game_state: GameState) -> Dict[str, Any]:
        """计算对抗策略"""

        # 选择最有效的攻击类型
        weakness_scores = student_analysis
        max_weakness = max(weakness_scores.values())
        target_weakness = max(weakness_scores, key=weakness_scores.get)

        # 根据目标弱点选择攻击类型
        if target_weakness == 'threshold_sensitivity':
            anomaly_type = 'stealth'  # 隐蔽攻击，接近阈值边界
            intensity = predicted_student['predicted_threshold'] * 0.9
        elif target_weakness == 'false_positive_vulnerability':
            anomaly_type = 'deceptive'  # 欺骗性攻击，诱导误报
            intensity = 0.6
        elif target_weakness == 'slow_adaptation':
            anomaly_type = 'adaptive'  # 自适应攻击，快速变化
            intensity = 0.7
        else:
            anomaly_type = 'aggressive'  # 激进攻击
            intensity = 0.8

        # 根据课程阶段调整强度
        stage_multipliers = {
            'foundation': 0.7,
            'adversarial': 1.0,
            'mastery': 1.3
        }
        stage_multiplier = stage_multipliers.get(game_state.curriculum_stage, 1.0)
        intensity *= stage_multiplier

        # 选择目标特征
        target_features = self._select_target_features(anomaly_type, student_analysis)

        # 选择逃避技术
        evasion_technique = self._select_evasion_technique(
            anomaly_type, predicted_student['predicted_adaptation']
        )

        # 生成参数
        generation_parameters = self._generate_parameters(
            anomaly_type, intensity, target_features
        )

        return {
            'type': anomaly_type,
            'intensity': np.clip(intensity, *self.intensity_range),
            'targets': target_features,
            'technique': evasion_technique,
            'parameters': generation_parameters
        }

    def _add_creativity(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """添加创新性"""
        # 随机变异策略参数
        if np.random.random() < 0.5:
            # 变异强度
            intensity_mutation = np.random.normal(0, 0.1)
            strategy['intensity'] = np.clip(
                strategy['intensity'] + intensity_mutation, *self.intensity_range
            )

        if np.random.random() < 0.3:
            # 变异攻击类型
            strategy['type'] = np.random.choice(self.anomaly_types)

        return strategy

    def _assess_pattern_weakness(self, game_state: GameState) -> float:
        """评估模式识别弱点"""
        recent_outcomes = game_state.historical_outcomes[-10:]
        if len(recent_outcomes) < 3:
            return 0.5

        # 分析学生对不同模式的检测能力
        pattern_successes = {}
        for outcome in recent_outcomes:
            pattern = outcome.get('anomaly_pattern', 'unknown')
            success = outcome.get('student_success', False)

            if pattern not in pattern_successes:
                pattern_successes[pattern] = []
            pattern_successes[pattern].append(success)

        # 计算最弱的模式识别能力
        if pattern_successes:
            pattern_rates = {p: np.mean(successes) for p, successes in pattern_successes.items()}
            min_success_rate = min(pattern_rates.values())
            return 1.0 - min_success_rate

        return 0.5

    def _select_target_features(self, anomaly_type: str,
                              student_analysis: Dict[str, float]) -> List[int]:
        """选择目标特征"""
        # 简化实现：基于异常类型选择特征
        feature_targets = {
            'stealth': [0, 2, 5, 7],      # 隐蔽特征
            'aggressive': [1, 3, 4, 6],   # 明显特征
            'adaptive': [0, 1, 2, 3, 4],  # 多特征
            'deceptive': [5, 6, 7, 8, 9]  # 欺骗特征
        }

        return feature_targets.get(anomaly_type, [0, 1, 2])

    def _select_evasion_technique(self, anomaly_type: str,
                                student_adaptation: str) -> str:
        """选择逃避技术"""
        if student_adaptation == 'fast':
            # 学生适应快，使用更复杂的技术
            techniques = ['morphing', 'multi_stage', 'camouflage']
        else:
            # 学生适应慢，使用简单技术
            techniques = ['direct', 'gradual', 'burst']

        return np.random.choice(techniques)

    def _generate_parameters(self, anomaly_type: str, intensity: float,
                           target_features: List[int]) -> Dict[str, Any]:
        """生成具体参数"""
        base_params = {
            'duration': max(1, int(5 * intensity)),
            'frequency': 0.1 + 0.4 * intensity,
            'amplitude': 0.2 + 0.6 * intensity
        }

        # 根据异常类型调整参数
        if anomaly_type == 'stealth':
            base_params['amplitude'] *= 0.7  # 降低幅度
            base_params['frequency'] *= 0.8  # 降低频率
        elif anomaly_type == 'aggressive':
            base_params['amplitude'] *= 1.3  # 增加幅度
            base_params['duration'] *= 0.7   # 缩短持续时间

        return base_params

    def _initialize_student_model(self) -> Dict[str, Any]:
        """初始化学生模型"""
        return {
            'detection_patterns': {},
            'threshold_estimates': [],
            'adaptation_patterns': {},
            'weakness_exploitation_history': []
        }

    def update_beliefs(self, game_state: GameState, outcomes: Dict[str, Any]):
        """更新对学生的信念"""
        # 更新学生能力估计
        student_success = outcomes.get('student_success', False)
        evasion_success = not student_success

        if 'student_capability' not in self.beliefs:
            self.beliefs['student_capability'] = 1.0 - float(evasion_success)
        else:
            student_performance = 1.0 - float(evasion_success)
            self.beliefs['student_capability'] = (
                (1 - self.belief_update_rate) * self.beliefs['student_capability'] +
                self.belief_update_rate * student_performance
            )

        # 记录成功率
        self.success_memory.append(evasion_success)
        if len(self.success_memory) > 50:  # 保持最近50次记录
            self.success_memory.pop(0)

        # 更新策略有效性
        strategy_effectiveness = 1.0 if evasion_success else 0.0
        self.performance_history.append(strategy_effectiveness)

        # 自适应调整参数
        if len(self.success_memory) > 10:
            recent_success_rate = np.mean(self.success_memory[-10:])
            if recent_success_rate < 0.3:  # 成功率太低，增加创新性
                self.creativity_factor = min(0.5, self.creativity_factor + 0.05)
            elif recent_success_rate > 0.8:  # 成功率太高，可能需要更平衡
                self.creativity_factor = max(0.1, self.creativity_factor - 0.02)
