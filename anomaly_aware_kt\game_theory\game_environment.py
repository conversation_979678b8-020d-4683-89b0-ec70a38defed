"""
博弈环境 - 整合数据集管理的三方博弈执行环境

博弈环境负责：
1. 协调三方智能体的交互
2. 管理数据集在博弈中的角色
3. 执行博弈轮次并计算结果
4. 维护博弈状态和历史
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import time
from dataclasses import dataclass

from .dataset_manager import GameDatasetManager, DataSample
from .game_agents import GameState


@dataclass
class GameRoundResult:
    """博弈轮次结果"""
    round_id: int
    sample_id: int
    
    # 原始数据
    original_sample: DataSample
    
    # 智能体策略
    teacher_strategy: Dict[str, Any]
    student_strategy: Dict[str, Any]
    adversary_strategy: Dict[str, Any]
    
    # 博弈过程
    adversary_generated_sample: torch.Tensor
    student_detection_result: bool
    student_confidence: float
    
    # 博弈结果
    student_success: bool           # 学生是否检测正确
    adversary_success: bool         # 对手是否成功逃避
    teacher_effectiveness: float    # 教师干预效果
    
    # 性能指标
    detection_accuracy: float
    false_positive_rate: float
    evasion_rate: float
    game_balance: float
    
    # 时间戳
    timestamp: float


class GameEnvironment:
    """
    博弈环境
    
    整合数据集管理器，提供完整的三方博弈执行环境。
    数据集在此环境中扮演多重角色，支持动态博弈过程。
    """
    
    def __init__(self, 
                 dataset_manager: GameDatasetManager,
                 environment_config: Dict[str, Any] = None,
                 device: str = 'cpu'):
        """
        初始化博弈环境
        
        Args:
            dataset_manager: 数据集管理器
            environment_config: 环境配置
            device: 计算设备
        """
        self.dataset_manager = dataset_manager
        self.config = environment_config or {}
        self.device = device
        
        # 博弈状态
        self.current_round = 0
        self.game_history = []
        self.performance_metrics = {
            'student_wins': 0,
            'adversary_wins': 0,
            'draws': 0,
            'total_rounds': 0
        }
        
        # 环境参数
        self.noise_level = self.config.get('noise_level', 0.01)
        self.enable_teacher_intervention = self.config.get('enable_teacher_intervention', True)
        self.real_time_adaptation = self.config.get('real_time_adaptation', True)
        
        print("🎮 博弈环境初始化完成")
        
    def execute_round(self,
                     teacher_strategy: Dict[str, Any],
                     student_strategy: Dict[str, Any],
                     adversary_strategy: Dict[str, Any],
                     game_state: GameState) -> GameRoundResult:
        """
        执行一轮博弈
        
        Args:
            teacher_strategy: 教师策略
            student_strategy: 学生策略
            adversary_strategy: 对手策略
            game_state: 当前博弈状态
            
        Returns:
            GameRoundResult: 博弈轮次结果
        """
        round_start_time = time.time()
        
        # 1. 数据集提供环境状态
        env_state = self.dataset_manager.get_game_environment_state(game_state)
        
        if env_state['sample'] is None:
            raise ValueError("No suitable sample available for current game state")
            
        original_sample = env_state['sample']
        
        # 2. 创建博弈战场
        battleground = self.dataset_manager.create_battleground(
            student_strategy, adversary_strategy, teacher_strategy
        )
        
        # 3. 对手生成异常
        adversary_result = self._execute_adversary_action(
            original_sample, adversary_strategy, battleground
        )
        
        # 4. 学生检测异常
        student_result = self._execute_student_action(
            adversary_result['generated_sample'], 
            student_strategy, 
            battleground,
            teacher_strategy if self.enable_teacher_intervention else None
        )
        
        # 5. 教师评估和干预
        teacher_result = self._execute_teacher_evaluation(
            original_sample, adversary_result, student_result, teacher_strategy
        )
        
        # 6. 计算博弈结果
        game_result = self._calculate_game_result(
            original_sample, adversary_result, student_result, teacher_result
        )
        
        # 7. 创建轮次结果
        round_result = GameRoundResult(
            round_id=self.current_round,
            sample_id=original_sample.metadata['id'],
            original_sample=original_sample,
            teacher_strategy=teacher_strategy,
            student_strategy=student_strategy,
            adversary_strategy=adversary_strategy,
            adversary_generated_sample=adversary_result['generated_sample'],
            student_detection_result=student_result['detection'],
            student_confidence=student_result['confidence'],
            student_success=game_result['student_success'],
            adversary_success=game_result['adversary_success'],
            teacher_effectiveness=teacher_result['effectiveness'],
            detection_accuracy=game_result['detection_accuracy'],
            false_positive_rate=game_result['false_positive_rate'],
            evasion_rate=game_result['evasion_rate'],
            game_balance=game_result['game_balance'],
            timestamp=round_start_time
        )
        
        # 8. 更新环境状态
        self._update_environment_state(round_result)
        
        # 9. 更新数据集使用统计
        self.dataset_manager.update_usage_statistics({
            'sample_id': original_sample.metadata['id'],
            'student_success': game_result['student_success'],
            'adversary_success': game_result['adversary_success'],
            'timestamp': round_start_time
        })
        
        self.current_round += 1
        
        return round_result
        
    def _execute_adversary_action(self,
                                 original_sample: DataSample,
                                 adversary_strategy: Dict[str, Any],
                                 battleground: Dict[str, Any]) -> Dict[str, Any]:
        """执行对手行动"""
        
        # 获取对手视角
        adversary_view = battleground['adversary_view']
        original_features = adversary_view['original_features']
        generation_context = adversary_view['generation_context']
        
        # 根据策略生成异常
        anomaly_type = generation_context['anomaly_type']
        intensity = generation_context['intensity']
        target_features = generation_context['target_features']
        technique = generation_context['evasion_technique']
        
        # 生成异常样本
        if anomaly_type == 'stealth':
            generated_sample = self._generate_stealth_anomaly(
                original_features, intensity, target_features
            )
        elif anomaly_type == 'aggressive':
            generated_sample = self._generate_aggressive_anomaly(
                original_features, intensity, target_features
            )
        elif anomaly_type == 'adaptive':
            generated_sample = self._generate_adaptive_anomaly(
                original_features, intensity, target_features, technique
            )
        elif anomaly_type == 'deceptive':
            generated_sample = self._generate_deceptive_anomaly(
                original_features, intensity, target_features
            )
        else:
            # 默认生成方式
            generated_sample = self._generate_default_anomaly(
                original_features, intensity
            )
            
        # 添加环境噪声
        if self.noise_level > 0:
            noise = torch.normal(0, self.noise_level, generated_sample.shape)
            generated_sample = generated_sample + noise
            
        return {
            'generated_sample': generated_sample,
            'anomaly_type': anomaly_type,
            'intensity': intensity,
            'technique': technique,
            'generation_success': True
        }
        
    def _execute_student_action(self,
                               generated_sample: torch.Tensor,
                               student_strategy: Dict[str, Any],
                               battleground: Dict[str, Any],
                               teacher_guidance: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """执行学生行动"""
        
        # 获取学生视角
        student_view = battleground['student_view']
        detection_context = student_view['detection_context']
        
        # 获取检测参数
        threshold = detection_context['detection_threshold']
        feature_weights = detection_context['feature_weights']
        confidence_level = detection_context['confidence_level']
        
        # 应用教师指导（如果有）
        if teacher_guidance and self.enable_teacher_intervention:
            threshold = self._apply_teacher_guidance(threshold, teacher_guidance)
            
        # 计算异常分数
        anomaly_score = self._calculate_anomaly_score(
            generated_sample, feature_weights, threshold
        )
        
        # 做出检测决策
        detection = anomaly_score > threshold
        confidence = self._calculate_detection_confidence(
            anomaly_score, threshold, confidence_level
        )
        
        return {
            'detection': detection,
            'anomaly_score': anomaly_score,
            'confidence': confidence,
            'threshold_used': threshold
        }
        
    def _execute_teacher_evaluation(self,
                                   original_sample: DataSample,
                                   adversary_result: Dict[str, Any],
                                   student_result: Dict[str, Any],
                                   teacher_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """执行教师评估"""
        
        # 评估学生表现
        student_correct = (student_result['detection'] == (original_sample.label == 1))
        
        # 评估对手表现
        adversary_successful = not student_correct
        
        # 评估博弈平衡
        balance_score = self._calculate_balance_score(student_correct, adversary_successful)
        
        # 计算教师干预效果
        intervention_type = teacher_strategy.get('intervention', {}).get('type', 'none')
        if intervention_type != 'none':
            effectiveness = self._calculate_intervention_effectiveness(
                student_result, teacher_strategy, student_correct
            )
        else:
            effectiveness = 0.5  # 无干预时的基准效果
            
        # 生成反馈
        feedback = self._generate_teacher_feedback(
            student_correct, adversary_successful, balance_score
        )
        
        return {
            'effectiveness': effectiveness,
            'balance_score': balance_score,
            'feedback': feedback,
            'intervention_applied': intervention_type != 'none'
        }
        
    def _calculate_game_result(self,
                              original_sample: DataSample,
                              adversary_result: Dict[str, Any],
                              student_result: Dict[str, Any],
                              teacher_result: Dict[str, Any]) -> Dict[str, Any]:
        """计算博弈结果"""
        
        # 基本结果
        true_label = original_sample.label == 1  # True if anomaly
        predicted_label = student_result['detection']
        
        student_success = (predicted_label == true_label)
        adversary_success = not student_success
        
        # 性能指标
        detection_accuracy = 1.0 if student_success else 0.0
        
        # 假阳性率（只有在真实为正常时才计算）
        if not true_label:
            false_positive_rate = 1.0 if predicted_label else 0.0
        else:
            false_positive_rate = 0.0
            
        # 逃避率
        evasion_rate = 1.0 if adversary_success else 0.0
        
        # 博弈平衡度
        game_balance = teacher_result['balance_score']
        
        return {
            'student_success': student_success,
            'adversary_success': adversary_success,
            'detection_accuracy': detection_accuracy,
            'false_positive_rate': false_positive_rate,
            'evasion_rate': evasion_rate,
            'game_balance': game_balance
        }
        
    def _generate_stealth_anomaly(self, features: torch.Tensor, intensity: float, targets: List[int]) -> torch.Tensor:
        """生成隐蔽型异常"""
        modified = features.clone()
        
        for target in targets:
            if target < len(modified):
                # 小幅度修改，难以察觉
                noise = torch.normal(0, 0.1 * intensity, (1,))
                modified[target] += noise
                
        return modified
        
    def _generate_aggressive_anomaly(self, features: torch.Tensor, intensity: float, targets: List[int]) -> torch.Tensor:
        """生成激进型异常"""
        modified = features.clone()
        
        for target in targets:
            if target < len(modified):
                # 大幅度修改
                modification = intensity * (2 * torch.rand(1) - 1)  # [-intensity, intensity]
                modified[target] += modification
                
        return modified
        
    def _generate_adaptive_anomaly(self, features: torch.Tensor, intensity: float, targets: List[int], technique: str) -> torch.Tensor:
        """生成自适应型异常"""
        modified = features.clone()
        
        if technique == 'morphing':
            # 渐变式修改
            for i, target in enumerate(targets):
                if target < len(modified):
                    factor = intensity * (i + 1) / len(targets)
                    modified[target] += factor * torch.randn(1)
        elif technique == 'multi_stage':
            # 多阶段修改
            for target in targets[:len(targets)//2]:
                if target < len(modified):
                    modified[target] += intensity * 0.5 * torch.randn(1)
        else:
            # 默认自适应
            for target in targets:
                if target < len(modified):
                    modified[target] += intensity * torch.randn(1)
                    
        return modified
        
    def _generate_deceptive_anomaly(self, features: torch.Tensor, intensity: float, targets: List[int]) -> torch.Tensor:
        """生成欺骗型异常"""
        modified = features.clone()
        
        # 在非目标特征上添加干扰，在目标特征上进行精确修改
        for i in range(len(modified)):
            if i in targets:
                # 目标特征：精确修改
                modified[i] += intensity * 0.3 * torch.randn(1)
            else:
                # 非目标特征：添加干扰
                modified[i] += intensity * 0.1 * torch.randn(1)
                
        return modified
        
    def _generate_default_anomaly(self, features: torch.Tensor, intensity: float) -> torch.Tensor:
        """生成默认异常"""
        noise = torch.normal(0, intensity * 0.2, features.shape)
        return features + noise
        
    def _calculate_anomaly_score(self, sample: torch.Tensor, weights: torch.Tensor, threshold: float) -> float:
        """计算异常分数"""
        
        # 确保维度匹配
        if len(weights) != len(sample):
            weights = weights[:len(sample)] if len(weights) > len(sample) else torch.cat([weights, torch.ones(len(sample) - len(weights))])
            
        # 加权异常分数
        weighted_features = sample * weights
        anomaly_score = torch.norm(weighted_features).item()
        
        # 归一化到[0,1]
        normalized_score = torch.sigmoid(torch.tensor(anomaly_score - threshold)).item()
        
        return normalized_score
        
    def _calculate_detection_confidence(self, score: float, threshold: float, confidence_level: float) -> float:
        """计算检测置信度"""
        distance_from_threshold = abs(score - threshold)
        confidence = min(1.0, confidence_level + distance_from_threshold)
        return confidence
        
    def _apply_teacher_guidance(self, threshold: float, guidance: Dict[str, Any]) -> float:
        """应用教师指导"""
        intervention = guidance.get('intervention', {})
        intervention_type = intervention.get('type', 'none')
        
        if intervention_type == 'hint_provision':
            # 提示：微调阈值
            adjustment = intervention.get('intensity', 0.5) * 0.1
            return threshold - adjustment  # 降低阈值，提高敏感性
        elif intervention_type == 'strategy_guidance':
            # 策略指导：更大调整
            adjustment = intervention.get('intensity', 0.5) * 0.2
            return threshold - adjustment
        elif intervention_type == 'challenge_boost':
            # 挑战提升：提高阈值
            adjustment = intervention.get('intensity', 0.5) * 0.1
            return threshold + adjustment
        else:
            return threshold
            
    def _calculate_balance_score(self, student_success: bool, adversary_success: bool) -> float:
        """计算博弈平衡分数"""
        if student_success and not adversary_success:
            return 0.6  # 学生略胜
        elif adversary_success and not student_success:
            return 0.4  # 对手略胜
        else:
            return 0.5  # 平衡
            
    def _calculate_intervention_effectiveness(self, student_result: Dict[str, Any], teacher_strategy: Dict[str, Any], student_correct: bool) -> float:
        """计算干预效果"""
        
        # 基础效果
        base_effectiveness = 0.7 if student_correct else 0.3
        
        # 根据干预类型调整
        intervention = teacher_strategy.get('intervention', {})
        intervention_intensity = intervention.get('intensity', 0.5)
        
        # 干预强度影响效果
        effectiveness = base_effectiveness + (intervention_intensity - 0.5) * 0.2
        
        return max(0.0, min(1.0, effectiveness))
        
    def _generate_teacher_feedback(self, student_success: bool, adversary_success: bool, balance: float) -> Dict[str, str]:
        """生成教师反馈"""
        
        if student_success:
            performance_feedback = "检测正确，表现良好"
        else:
            performance_feedback = "检测错误，需要改进"
            
        if balance > 0.6:
            balance_feedback = "当前偏向学生优势"
        elif balance < 0.4:
            balance_feedback = "当前偏向对手优势"
        else:
            balance_feedback = "博弈相对平衡"
            
        return {
            'performance': performance_feedback,
            'balance': balance_feedback,
            'suggestion': self._generate_improvement_suggestion(student_success, balance)
        }
        
    def _generate_improvement_suggestion(self, student_success: bool, balance: float) -> str:
        """生成改进建议"""
        if not student_success:
            if balance < 0.4:
                return "建议降低检测阈值，提高敏感性"
            else:
                return "建议分析特征权重，优化检测策略"
        else:
            if balance > 0.6:
                return "表现良好，可以尝试更具挑战性的任务"
            else:
                return "继续保持当前策略"
                
    def _update_environment_state(self, round_result: GameRoundResult):
        """更新环境状态"""
        
        # 更新历史记录
        self.game_history.append(round_result)
        
        # 更新性能统计
        self.performance_metrics['total_rounds'] += 1
        
        if round_result.student_success and not round_result.adversary_success:
            self.performance_metrics['student_wins'] += 1
        elif round_result.adversary_success and not round_result.student_success:
            self.performance_metrics['adversary_wins'] += 1
        else:
            self.performance_metrics['draws'] += 1
            
        # 保持历史记录长度
        if len(self.game_history) > 1000:
            self.game_history = self.game_history[-1000:]
            
    def get_environment_statistics(self) -> Dict[str, Any]:
        """获取环境统计信息"""
        
        recent_results = self.game_history[-50:] if len(self.game_history) >= 50 else self.game_history
        
        if recent_results:
            avg_detection_accuracy = np.mean([r.detection_accuracy for r in recent_results])
            avg_evasion_rate = np.mean([r.evasion_rate for r in recent_results])
            avg_game_balance = np.mean([r.game_balance for r in recent_results])
            avg_teacher_effectiveness = np.mean([r.teacher_effectiveness for r in recent_results])
        else:
            avg_detection_accuracy = avg_evasion_rate = avg_game_balance = avg_teacher_effectiveness = 0.0
            
        return {
            'total_rounds': self.current_round,
            'performance_metrics': self.performance_metrics,
            'recent_averages': {
                'detection_accuracy': avg_detection_accuracy,
                'evasion_rate': avg_evasion_rate,
                'game_balance': avg_game_balance,
                'teacher_effectiveness': avg_teacher_effectiveness
            },
            'dataset_statistics': self.dataset_manager.get_dataset_statistics()
        }
