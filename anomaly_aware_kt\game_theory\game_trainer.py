"""
博弈论异常检测训练器

实现教师-学生-对手三方博弈的训练流程，集成：
1. 多智能体博弈环境
2. 自适应课程学习
3. 纳什均衡求解
4. 性能评估和监控
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from collections import defaultdict

from .game_agents import TeacherAgent, StudentAgent, AdversaryAgent, GameState
from .game_environment import GameEnvironment


@dataclass
class TrainingConfig:
    """训练配置"""
    max_episodes: int = 1000
    max_rounds_per_episode: int = 50
    convergence_threshold: float = 0.01
    performance_window: int = 20
    save_interval: int = 100
    log_interval: int = 10


@dataclass
class GameResult:
    """博弈结果"""
    episode: int
    round_number: int
    teacher_strategy: Dict[str, Any]
    student_strategy: Dict[str, Any]
    adversary_strategy: Dict[str, Any]
    outcomes: Dict[str, Any]
    performance_metrics: Dict[str, float]


class GameTheoreticTrainer:
    """
    博弈论异常检测训练器

    协调教师-学生-对手三方博弈的训练过程，
    通过不完全信息博弈和课程学习实现鲁棒的异常检测器训练。
    """

    def __init__(self,
                 config: TrainingConfig,
                 teacher_config: Dict[str, Any],
                 student_config: Dict[str, Any],
                 adversary_config: Dict[str, Any],
                 device: str = 'cpu'):
        """
        初始化博弈训练器

        Args:
            config: 训练配置
            teacher_config: 教师智能体配置
            student_config: 学生智能体配置
            adversary_config: 对手智能体配置
            device: 计算设备
        """
        self.config = config
        self.device = device

        # 创建智能体
        self.teacher = TeacherAgent("teacher", teacher_config)
        self.student = StudentAgent("student", student_config)
        self.adversary = AdversaryAgent("adversary", adversary_config)

        # 创建博弈环境
        self.environment = GameEnvironment(device=device)

        # 训练状态
        self.current_episode = 0
        self.training_history = []
        self.performance_history = defaultdict(list)
        self.convergence_metrics = []

        # 日志设置
        self.logger = self._setup_logger()

    def train(self) -> Dict[str, Any]:
        """
        执行博弈训练

        Returns:
            训练结果和统计信息
        """
        self.logger.info("🎮 开始博弈论异常检测训练")
        self.logger.info(f"📊 配置: {self.config.max_episodes} episodes, {self.config.max_rounds_per_episode} rounds/episode")

        try:
            for episode in range(self.config.max_episodes):
                self.current_episode = episode

                # 执行一个训练回合
                episode_results = self._train_episode()

                # 记录结果
                self.training_history.extend(episode_results)

                # 更新性能指标
                self._update_performance_metrics(episode_results)

                # 检查收敛
                if self._check_convergence():
                    self.logger.info(f"🎯 训练在第 {episode} 回合收敛")
                    break

                # 定期日志和保存
                if episode % self.config.log_interval == 0:
                    self._log_progress(episode)

                if episode % self.config.save_interval == 0:
                    self._save_checkpoint(episode)

            # 训练完成
            final_results = self._finalize_training()
            self.logger.info("✅ 博弈训练完成")

            return final_results

        except Exception as e:
            self.logger.error(f"❌ 训练过程中出现错误: {e}")
            raise

    def _train_episode(self) -> List[GameResult]:
        """训练一个回合"""
        episode_results = []

        # 初始化回合状态
        game_state = GameState(
            round_number=0,
            student_performance=self.student.get_performance_metrics(),
            adversary_success_rate=self._calculate_adversary_success_rate(),
            curriculum_stage='foundation',
            difficulty_level=0.5,
            historical_outcomes=[]
        )

        for round_num in range(self.config.max_rounds_per_episode):
            # 执行一轮博弈
            round_result = self._execute_game_round(game_state, round_num)
            episode_results.append(round_result)

            # 更新博弈状态
            game_state = self._update_game_state(game_state, round_result)

            # 智能体学习和适应
            self._update_agents(game_state, round_result)

        return episode_results

    def _execute_game_round(self, game_state: GameState, round_num: int) -> GameResult:
        """执行一轮博弈"""

        # 阶段1：教师制定策略
        teacher_strategy = self.teacher.select_strategy(game_state)

        # 阶段2：学生和对手同时选择策略（不完全信息博弈）
        student_strategy = self.student.select_strategy(game_state)
        adversary_strategy = self.adversary.select_strategy(game_state)

        # 阶段3：执行博弈并计算结果
        outcomes = self.environment.execute_round(
            teacher_strategy, student_strategy, adversary_strategy
        )

        # 阶段4：计算性能指标
        performance_metrics = self._calculate_performance_metrics(
            teacher_strategy, student_strategy, adversary_strategy, outcomes
        )

        return GameResult(
            episode=self.current_episode,
            round_number=round_num,
            teacher_strategy=teacher_strategy,
            student_strategy=student_strategy,
            adversary_strategy=adversary_strategy,
            outcomes=outcomes,
            performance_metrics=performance_metrics
        )

    def _update_game_state(self, current_state: GameState, round_result: GameResult) -> GameState:
        """更新博弈状态"""

        # 更新历史记录
        new_outcome = {
            'round': round_result.round_number,
            'student_success': round_result.outcomes.get('student_success', False),
            'adversary_success': round_result.outcomes.get('adversary_success', False),
            'teacher_effectiveness': round_result.performance_metrics.get('teacher_effectiveness', 0.0),
            'student_performance': round_result.performance_metrics.get('student_performance', 0.0),
            'adversary_performance': round_result.performance_metrics.get('adversary_performance', 0.0)
        }

        updated_history = current_state.historical_outcomes + [new_outcome]
        if len(updated_history) > 50:  # 保持最近50轮记录
            updated_history = updated_history[-50:]

        # 更新课程阶段
        new_stage = self._determine_curriculum_stage(
            round_result.performance_metrics, current_state.curriculum_stage
        )

        # 更新难度级别
        new_difficulty = self._adjust_difficulty_level(
            round_result.performance_metrics, current_state.difficulty_level
        )

        return GameState(
            round_number=round_result.round_number + 1,
            student_performance=round_result.performance_metrics,
            adversary_success_rate=round_result.outcomes.get('adversary_success_rate', 0.0),
            curriculum_stage=new_stage,
            difficulty_level=new_difficulty,
            historical_outcomes=updated_history
        )

    def _update_agents(self, game_state: GameState, round_result: GameResult):
        """更新智能体信念和策略"""

        # 准备更新数据
        outcomes = {
            'student_success': round_result.outcomes.get('student_success', False),
            'adversary_success': round_result.outcomes.get('adversary_success', False),
            'teaching_effectiveness': round_result.performance_metrics.get('teacher_effectiveness', 0.0),
            'student_performance': round_result.performance_metrics.get('student_performance', 0.0),
            'adversary_performance': round_result.performance_metrics.get('adversary_performance', 0.0),
            'detection_result': round_result.outcomes.get('detection_result', False),
            'adversary_strategy': round_result.adversary_strategy,
            'student_strategy': round_result.student_strategy
        }

        # 更新各智能体
        self.teacher.update_beliefs(game_state, outcomes)
        self.student.update_beliefs(game_state, outcomes)
        self.adversary.update_beliefs(game_state, outcomes)

    def _calculate_performance_metrics(self,
                                     teacher_strategy: Dict[str, Any],
                                     student_strategy: Dict[str, Any],
                                     adversary_strategy: Dict[str, Any],
                                     outcomes: Dict[str, Any]) -> Dict[str, float]:
        """计算性能指标"""

        # 学生性能指标
        student_performance = {
            'detection_accuracy': outcomes.get('detection_accuracy', 0.0),
            'false_positive_rate': outcomes.get('false_positive_rate', 0.0),
            'learning_speed': outcomes.get('learning_speed', 0.0),
            'stability': outcomes.get('stability', 0.0)
        }

        # 对手性能指标
        adversary_performance = {
            'evasion_rate': outcomes.get('evasion_rate', 0.0),
            'strategy_diversity': outcomes.get('strategy_diversity', 0.0),
            'adaptation_speed': outcomes.get('adaptation_speed', 0.0)
        }

        # 教师性能指标
        teacher_effectiveness = self._calculate_teacher_effectiveness(
            teacher_strategy, student_performance, adversary_performance
        )

        # 博弈平衡指标
        game_balance = self._calculate_game_balance(
            student_performance, adversary_performance
        )

        return {
            'student_performance': np.mean(list(student_performance.values())),
            'adversary_performance': np.mean(list(adversary_performance.values())),
            'teacher_effectiveness': teacher_effectiveness,
            'game_balance': game_balance,
            'overall_quality': (teacher_effectiveness + game_balance) / 2
        }

    def _calculate_teacher_effectiveness(self,
                                       teacher_strategy: Dict[str, Any],
                                       student_perf: Dict[str, float],
                                       adversary_perf: Dict[str, float]) -> float:
        """计算教师有效性"""

        # 基于学生进步评估
        student_progress = student_perf.get('learning_speed', 0.0)

        # 基于课程设计质量评估
        curriculum_quality = self._evaluate_curriculum_quality(teacher_strategy)

        # 基于博弈平衡评估
        balance_quality = abs(0.5 - adversary_perf.get('evasion_rate', 0.5))
        balance_score = 1.0 - 2 * balance_quality  # 越接近0.5越好

        # 综合评估
        effectiveness = (
            0.4 * student_progress +
            0.3 * curriculum_quality +
            0.3 * balance_score
        )

        return max(0.0, min(1.0, effectiveness))

    def _calculate_game_balance(self,
                              student_perf: Dict[str, float],
                              adversary_perf: Dict[str, float]) -> float:
        """计算博弈平衡性"""

        student_score = np.mean(list(student_perf.values()))
        adversary_score = np.mean(list(adversary_perf.values()))

        # 理想情况下，学生和对手应该势均力敌
        balance = 1.0 - abs(student_score - adversary_score)

        return max(0.0, balance)

    def _evaluate_curriculum_quality(self, teacher_strategy: Dict[str, Any]) -> float:
        """评估课程质量"""

        # 简化评估：基于难度调整的合理性
        difficulty = teacher_strategy.get('difficulty_level', 0.5)
        stage = teacher_strategy.get('curriculum_stage', 'foundation')

        # 不同阶段的理想难度范围
        ideal_difficulties = {
            'foundation': (0.3, 0.6),
            'adversarial': (0.5, 0.8),
            'mastery': (0.7, 0.9)
        }

        ideal_range = ideal_difficulties.get(stage, (0.4, 0.7))

        if ideal_range[0] <= difficulty <= ideal_range[1]:
            return 1.0
        else:
            # 计算偏离程度
            if difficulty < ideal_range[0]:
                deviation = ideal_range[0] - difficulty
            else:
                deviation = difficulty - ideal_range[1]

            return max(0.0, 1.0 - deviation * 2)

    def _calculate_adversary_success_rate(self) -> float:
        """计算对手成功率"""
        if not self.adversary.success_memory:
            return 0.5

        recent_successes = self.adversary.success_memory[-10:]
        return np.mean(recent_successes)

    def _determine_curriculum_stage(self,
                                  performance_metrics: Dict[str, float],
                                  current_stage: str) -> str:
        """确定课程阶段"""

        student_performance = performance_metrics.get('student_performance', 0.0)
        game_balance = performance_metrics.get('game_balance', 0.0)

        # 阶段转换逻辑
        if current_stage == 'foundation':
            if student_performance > 0.7 and game_balance > 0.6:
                return 'adversarial'
        elif current_stage == 'adversarial':
            if student_performance > 0.8 and game_balance > 0.7:
                return 'mastery'
            elif student_performance < 0.5:
                return 'foundation'
        elif current_stage == 'mastery':
            if student_performance < 0.6:
                return 'adversarial'

        return current_stage

    def _adjust_difficulty_level(self,
                               performance_metrics: Dict[str, float],
                               current_difficulty: float) -> float:
        """调整难度级别"""

        student_performance = performance_metrics.get('student_performance', 0.0)
        game_balance = performance_metrics.get('game_balance', 0.0)

        # 基于表现调整难度
        if student_performance > 0.8:
            difficulty_delta = 0.05  # 增加难度
        elif student_performance < 0.4:
            difficulty_delta = -0.05  # 降低难度
        else:
            difficulty_delta = 0.0

        # 基于平衡性调整
        if game_balance < 0.4:
            difficulty_delta *= 0.5  # 减缓调整

        new_difficulty = current_difficulty + difficulty_delta
        return np.clip(new_difficulty, 0.1, 0.9)

    def _update_performance_metrics(self, episode_results: List[GameResult]):
        """更新性能指标"""

        for result in episode_results:
            metrics = result.performance_metrics

            for key, value in metrics.items():
                self.performance_history[key].append(value)

                # 保持历史记录长度
                if len(self.performance_history[key]) > 1000:
                    self.performance_history[key] = self.performance_history[key][-1000:]

    def _check_convergence(self) -> bool:
        """检查训练收敛"""

        if len(self.performance_history['overall_quality']) < self.config.performance_window:
            return False

        # 计算最近性能的方差
        recent_performance = self.performance_history['overall_quality'][-self.config.performance_window:]
        performance_variance = np.var(recent_performance)

        # 检查是否收敛
        converged = performance_variance < self.config.convergence_threshold

        self.convergence_metrics.append({
            'episode': self.current_episode,
            'variance': performance_variance,
            'converged': converged
        })

        return converged

    def _log_progress(self, episode: int):
        """记录训练进度"""

        if not self.performance_history['overall_quality']:
            return

        recent_quality = np.mean(self.performance_history['overall_quality'][-10:])
        recent_student = np.mean(self.performance_history['student_performance'][-10:])
        recent_adversary = np.mean(self.performance_history['adversary_performance'][-10:])
        recent_teacher = np.mean(self.performance_history['teacher_effectiveness'][-10:])

        self.logger.info(f"📊 Episode {episode}:")
        self.logger.info(f"   整体质量: {recent_quality:.3f}")
        self.logger.info(f"   学生表现: {recent_student:.3f}")
        self.logger.info(f"   对手表现: {recent_adversary:.3f}")
        self.logger.info(f"   教师有效性: {recent_teacher:.3f}")

    def _save_checkpoint(self, episode: int):
        """保存检查点"""
        checkpoint = {
            'episode': episode,
            'teacher_state': self.teacher.__dict__,
            'student_state': self.student.__dict__,
            'adversary_state': self.adversary.__dict__,
            'performance_history': dict(self.performance_history),
            'training_history': self.training_history[-100:]  # 保存最近100轮
        }

        checkpoint_path = f"game_checkpoint_episode_{episode}.pt"
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"💾 保存检查点: {checkpoint_path}")

    def _finalize_training(self) -> Dict[str, Any]:
        """完成训练并返回结果"""

        final_results = {
            'total_episodes': self.current_episode + 1,
            'convergence_achieved': len(self.convergence_metrics) > 0 and self.convergence_metrics[-1]['converged'],
            'final_performance': {
                key: np.mean(values[-10:]) if values else 0.0
                for key, values in self.performance_history.items()
            },
            'agent_final_states': {
                'teacher': self.teacher.get_performance_metrics(),
                'student': self.student.get_performance_metrics(),
                'adversary': self.adversary.get_performance_metrics()
            },
            'training_statistics': self._calculate_training_statistics()
        }

        return final_results

    def _calculate_training_statistics(self) -> Dict[str, Any]:
        """计算训练统计信息"""

        stats = {}

        for key, values in self.performance_history.items():
            if values:
                stats[key] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'final': np.mean(values[-10:]) if len(values) >= 10 else np.mean(values)
                }

        return stats

    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('GameTheoreticTrainer')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger
