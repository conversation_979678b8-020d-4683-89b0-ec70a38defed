#!/usr/bin/env python3
"""
测试重构后的导入是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_game_theory_imports():
    """测试博弈论模块导入"""
    print("🎮 测试博弈论模块导入...")

    try:
        from game_theory.game_agents import TeacherAgent, StudentAgent, AdversaryAgent, GameState
        from game_theory.game_environment import GameEnvironment
        from game_theory.game_trainer import GameTheoreticTrainer, TrainingConfig
        print("✅ 博弈论模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 博弈论模块导入失败: {e}")
        return False

def test_anomaly_generation_imports():
    """测试异常生成模块导入"""
    print("🔬 测试异常生成模块导入...")

    try:
        from anomaly_generation.generators.scientific_generator import ScientificAnomalyGenerator
        from anomaly_generation.irt_modeling.irt_model import IRTDifficultyModel
        from anomaly_generation.quality.quality_controller import QualityController
        from anomaly_generation.taxonomy.cognitive_load import CognitiveLoadAnomaly
        print("✅ 异常生成模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 异常生成模块导入失败: {e}")
        return False

def test_core_module_imports():
    """测试核心模块导入"""
    print("🧠 测试核心模块导入...")

    try:
        from anomaly_kt import AnomalyAwareDTransformer, CausalAnomalyDetector
        from anomaly_kt.generator import AnomalyGenerator
        from anomaly_kt.trainer import AnomalyAwareTrainer
        print("✅ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False

def test_top_level_imports():
    """测试顶级导入"""
    print("📦 测试顶级导入...")

    try:
        import anomaly_aware_kt
        print(f"   版本: {anomaly_aware_kt.__version__}")
        print(f"   作者: {anomaly_aware_kt.__author__}")
        print("✅ 顶级导入成功")
        return True
    except ImportError as e:
        print(f"❌ 顶级导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("⚙️ 测试基本功能...")

    try:
        # 测试创建教师智能体
        from game_theory.game_agents import TeacherAgent, GameState

        teacher_config = {
            'learning_rate': 0.01,
            'stage_thresholds': {
                'foundation_to_adversarial': 0.7,
                'adversarial_to_mastery': 0.8
            }
        }

        teacher = TeacherAgent("test_teacher", teacher_config)

        # 测试创建游戏状态
        game_state = GameState(
            round_number=1,
            student_performance={'detection_accuracy': 0.6},
            adversary_success_rate=0.4,
            curriculum_stage='foundation',
            difficulty_level=0.5,
            historical_outcomes=[]
        )

        # 测试数据集特定课程设计
        dataset_profile = {
            'dataset_type': 'knowledge_tracing',
            'domain_complexity': 0.6,
            'temporal_dependency': True
        }

        curriculum = teacher.design_dataset_specific_curriculum(dataset_profile, game_state)
        print(f"   课程哲学: {curriculum['curriculum_philosophy']}")
        print("✅ 基本功能测试成功")
        return True

    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试重构后的项目结构")
    print("=" * 50)

    tests = [
        test_top_level_imports,
        test_game_theory_imports,
        test_anomaly_generation_imports,
        test_core_module_imports,
        test_basic_functionality
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        print("\n📁 新的项目结构:")
        print("anomaly_aware_kt/")
        print("├── game_theory/           # 博弈论模块 (已移动)")
        print("├── anomaly_generation/    # 异常生成模块 (已移动)")
        print("├── anomaly_kt/           # 核心知识追踪模块")
        print("├── docs/                 # 文档")
        print("├── examples/             # 示例代码")
        print("├── scripts/              # 脚本")
        print("└── configs/              # 配置文件")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
