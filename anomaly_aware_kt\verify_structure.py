#!/usr/bin/env python3
"""
验证重构后的项目结构
"""

import os
from pathlib import Path

def check_directory_structure():
    """检查目录结构"""
    print("📁 检查项目目录结构...")
    
    expected_dirs = [
        "game_theory",
        "anomaly_generation", 
        "anomaly_kt",
        "docs",
        "examples",
        "scripts",
        "configs"
    ]
    
    current_dir = Path(".")
    
    for dir_name in expected_dirs:
        dir_path = current_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"✅ {dir_name}/ 存在")
        else:
            print(f"❌ {dir_name}/ 不存在")
    
    print()

def check_game_theory_files():
    """检查博弈论模块文件"""
    print("🎮 检查博弈论模块文件...")
    
    game_theory_files = [
        "game_theory/__init__.py",
        "game_theory/game_agents.py",
        "game_theory/game_environment.py", 
        "game_theory/game_trainer.py",
        "game_theory/dataset_manager.py"
    ]
    
    for file_path in game_theory_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
    
    print()

def check_anomaly_generation_files():
    """检查异常生成模块文件"""
    print("🔬 检查异常生成模块文件...")
    
    anomaly_gen_files = [
        "anomaly_generation/__init__.py",
        "anomaly_generation/generators/scientific_generator.py",
        "anomaly_generation/irt_modeling/irt_model.py",
        "anomaly_generation/quality/quality_controller.py",
        "anomaly_generation/taxonomy/cognitive_load.py"
    ]
    
    for file_path in anomaly_gen_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
    
    print()

def check_old_directories_removed():
    """检查旧目录是否已删除"""
    print("🗑️ 检查旧目录是否已删除...")
    
    old_dirs = [
        "anomaly_kt/game_theory",
        "anomaly_kt/anomaly_generation"
    ]
    
    for dir_path in old_dirs:
        if Path(dir_path).exists():
            print(f"⚠️ {dir_path} 仍然存在（应该已删除）")
        else:
            print(f"✅ {dir_path} 已删除")
    
    print()

def test_basic_import():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    
    try:
        # 测试教师智能体导入和基本功能
        import sys
        sys.path.append(".")
        
        from game_theory.game_agents import TeacherAgent, GameState
        
        # 创建教师智能体
        teacher_config = {
            'learning_rate': 0.01,
            'stage_thresholds': {
                'foundation_to_adversarial': 0.7,
                'adversarial_to_mastery': 0.8
            }
        }
        
        teacher = TeacherAgent("test_teacher", teacher_config)
        
        # 创建游戏状态
        game_state = GameState(
            round_number=1,
            student_performance={'detection_accuracy': 0.6},
            adversary_success_rate=0.4,
            curriculum_stage='foundation',
            difficulty_level=0.5,
            historical_outcomes=[]
        )
        
        # 测试数据集特定课程设计
        dataset_profile = {
            'dataset_type': 'knowledge_tracing',
            'domain_complexity': 0.6,
            'temporal_dependency': True
        }
        
        curriculum = teacher.design_dataset_specific_curriculum(dataset_profile, game_state)
        print(f"✅ 成功创建教师智能体并设计课程: {curriculum['curriculum_philosophy']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 验证重构后的项目结构")
    print("=" * 60)
    
    # 检查目录结构
    check_directory_structure()
    
    # 检查具体文件
    check_game_theory_files()
    check_anomaly_generation_files()
    
    # 检查旧目录是否删除
    check_old_directories_removed()
    
    # 测试基本功能
    import_success = test_basic_import()
    
    print("=" * 60)
    print("📊 重构验证结果:")
    
    if import_success:
        print("🎉 重构成功！")
        print("\n✅ 主要改进:")
        print("   • game_theory 模块已移动到顶级目录")
        print("   • anomaly_generation 模块已移动到顶级目录") 
        print("   • 导入路径已更新")
        print("   • 基本功能正常工作")
        
        print("\n📁 新的项目结构:")
        print("anomaly_aware_kt/")
        print("├── game_theory/           # 博弈论模块 (已移动)")
        print("├── anomaly_generation/    # 异常生成模块 (已移动)")
        print("├── anomaly_kt/           # 核心知识追踪模块")
        print("├── docs/                 # 文档")
        print("├── examples/             # 示例代码")
        print("├── scripts/              # 脚本")
        print("└── configs/              # 配置文件")
        
        print("\n🔧 后续工作:")
        print("   • 更新所有脚本中的导入路径")
        print("   • 更新文档中的导入示例")
        print("   • 测试完整的训练流程")
        
    else:
        print("⚠️ 重构部分成功，但需要进一步调试导入问题")
    
    return import_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
